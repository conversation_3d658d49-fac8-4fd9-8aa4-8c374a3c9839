class FeishuSyncUserJob < ApplicationJob
  def perform(organization_id)
    feishu = Feishu.new(organization_id)
    users = Organization.find(organization_id).users.where(feishu_user_id: nil).where.not(phone: nil)
    user_list = users.pluck(:phone)

    user_list.each_slice(10) do |phones|
      feishu_user_id_list = feishu.get_users_id(phones)
      phones.zip(feishu_user_id_list).each do |phone, feishu_user_id|
        User.find_by(phone: phone).update(feishu_user_id: feishu_user_id)
      end
    end

  end
end
