class UserPolicy < ApplicationPolicy
  def index?
    check_auth("users", "index")
  end

  def create?
    check_auth("users", "create")
  end

  def update?
    check_auth("users", "update")
  end

  def destroy?
    check_auth("users", "destroy")
  end

  def new?
    create?
  end

  def edit?
    update?
  end

  def show?
    true
  end

  def sync_feishu?
    check_auth("users", "sync_feishu")
  end
end
