.flex{
  display:flex
}

.flex-item{
  display: flex;
  align-items: center;
}

.flex-item-around{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.project-form{
  display:flex;
  gap:1rem;
}
.project-form .layui-form{
  width:50%
}
.project-form .layui-form-label,
#project_form .layui-form-label{
  width: 85px;
  padding: 8px 12px;
}

.wd-50{
  width:50%
}

.jh-btn-normal{
  background-color: #2468f2;
}
.jh-btn-danger{
  background-color: #f2b424;
}

.font-11{
  font-size: 11px;
}

.project-show-title{
  /* font-size: 16px; */
  font-weight: 500;
}

.project-show-number{
  font-size: 20px;
  margin-right: 1px;
}

.p-l-3{
  padding-left: 3px;
}

.m-t-13{
  margin-top: 13px;
}

/* 步骤条 */
.progress-steps {
  display: flex;
  overflow-x: auto; /* 支持横向滚动 */
  padding: 40px 0;
  position: relative;
  gap: 60px; /* 调整此值来改变步骤之间的距离 */
  flex-wrap: wrap;
  justify-content: center;
}

.step {
  text-align: center;
  min-width: 100px; /* 确保每一步有足够的宽度 */
  flex-shrink: 0; /* 防止步骤被压缩 */
  position: relative;
}

.circle {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 50%;
  display: inline-block;
  margin-bottom: 10px;
  background-color: #ccc;
  color: white;
  position: relative;
  z-index: 1;
}

/* 添加 ::after 创建连接线 */
.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 28%;
  left: calc(79%);
  width: calc(100%);
  height: 1px;
  background-color: #ccc;
  transform: translateY(-50%);
  z-index: 0;
}

/* 根据状态修改颜色 */
.step.finished .circle {
  background-color: #e6f7ff;
  color: #1890ff;
}
.step.finished::after {
  background-color: #1890ff;
}

.step.discontinued .circle{
  background-color: #ff4d4f;
  color: #e6f7ff;
}
/* .step.discontinued::after {
  background-color: #ff4d4f;
} */

.step.in-progress .circle {
  background-color: #1890ff;
  color: white;
}
/* .step.in-progress::after {
  background-color: #1890ff;
} */

.step.waiting .circle {
  background-color: rgba(0, 0, 0, 0.06);;
  color: #595959;
}
.step.waiting::after {
  background-color: #d9d9d9;
}
.step.in-progress span, .step.finished span {
  color: rgba(0, 0, 0, 0.88);
}
.step.waiting span {
  color: rgba(0, 0, 0, 0.45);;
}
.step.flex-step{
  display: flex;
}
.step.flex-step .label{
  margin-top: 8px;
  margin-left: 2px;
}

.step.flex-step .circle {
  margin-bottom: 30px;
}

.flex-steps .step:not(:last-child)::after {
  top: 22%;
}

/* end */

.m-l-10{
  margin-left: 10px;
}

.m-l-5{
  margin-left: 5px;
}

.card-title{
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  font-family: Microsoft YaHei;
}

.card-child-title{
  font-weight: 400;
  color: #999999;
  font-size: 12px;
  font-family: Microsoft YaHei;
}

.card-count{
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #333333;
}

.notice_count{
  height: 13px;
  line-height: 13px;
  font-size: 9px;
  margin-left: 0px !important;
  padding: 0px 4px;
}

.fr-second-toolbar a{
  display: none;
}
.open_refresh_status .layui-form-switch{
  margin-top: 0px;
}

.ck-powered-by {
  display: none !important;
}

.ck-evaluation-badge{
  display: none !important;
}