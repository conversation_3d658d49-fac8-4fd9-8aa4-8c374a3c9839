/* 全局样式 - 添加 seven-container 作为作用域容器 */
.seven-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f7fa;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5715;
}

/* 布局容器 */
.seven-flex-container {
  display: flex;
  gap: 8px;
  height: calc(100vh - 60px);
}

/* 左侧详情面板 */
.seven-detail-panel {
  flex: 2.5;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  margin-left: 3px;
}

/* 右侧面板 */
.seven-side-panel {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 卡片样式 */
.seven-card {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.06);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.seven-card-header {
  padding: 4px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fafafa;
  height: 38px;
}

.seven-card-title {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.seven-card-body {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

/* 信息网格 */
.seven-ticket-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.seven-info-item {
  margin-bottom: 12px;
}

.seven-info-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 4px;
  font-weight: 400;
}

.seven-info-value {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  min-height: 22px;
}

/* 状态标签 */
.seven-tag {
  display: inline-block;
  padding: 0 7px;
  font-size: 12px;
  line-height: 20px;
  border-radius: 2px;
  border: 1px solid transparent;
}

.seven-tag-blue {
  background: #e6f4ff;
  border-color: #91caff;
  color: #1677ff;
}

.seven-tag-red {
  background: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
}

.seven-tag-green {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.seven-tag-yellow {
  background: #fffbe6;
  border-color: #ffe58f;
  color: #faad14;
}

/* 描述框 */
.seven-description-box {
  background: #fafafa;
  padding: 16px;
  border-radius: 2px;
  border-left: 3px solid #1677ff;
  margin-top: 8px;
  line-height: 1.7;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

/* 附件样式 */
.seven-file-attachment {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  background: #fafafa;
  border-radius: 2px;
  border: 1px solid #f0f0f0;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  text-decoration: none;
  transition: all 0.3s;
}

.seven-file-attachment:hover {
  background: #f0f0f0;
  border-color: #d9d9d9;
}

/* 时间线样式 */
.seven-timeline {
  position: relative;
  padding: 8px 0;
}

.seven-timeline::before {
  content: '';
  position: absolute;
  left: 16px;
  top: 0;
  bottom: 0;
  width: 1px;
  background: #f0f0f0;
}

.seven-timeline-item {
  position: relative;
  margin-bottom: 20px;
  padding-left: 40px;
}

.seven-timeline-dot {
  position: absolute;
  left: 10px;
  top: 4px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #1677ff;
  z-index: 1;
}

.seven-timeline-date {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 4px;
  font-weight: 400;
}

.seven-timeline-content {
  background: #fafafa;
  padding: 12px;
  border-radius: 2px;
  border: 1px solid #f0f0f0;
}

/* 评论样式 */
.seven-comment {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.seven-comment:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.seven-comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #1677ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  flex-shrink: 0;
  font-size: 14px;
}

.seven-comment-content {
  flex: 1;
}

.seven-comment-user {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.seven-comment-time {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.seven-comment-text {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.7;
  isolation: isolate;
  contain: content;
  /* z-index: 100; */
  position: relative;
}

/* 评论表单 */
.seven-comment-form {
  margin-top: auto;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  padding: 16px 0;
}

.seven-comment-form textarea {
  width: 97%;
  height: 100px;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  resize: vertical;
  margin-bottom: 8px;
  font-size: 14px;
  transition: all 0.3s;
}

.seven-comment-form textarea:focus {
  outline: none;
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.seven-comment-form button {
  padding: 0px 16px;
  background-color: #1677ff;
  color: white;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 13px;
  float: right;
}

.seven-comment-form button:hover {
  background-color: #4096ff;
}

/* 按钮样式 */
.seven-action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.seven-btn {
  padding: 4px 12px;
  border-radius: 2px;
  font-weight: 400;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s;
  border: 1px solid transparent;
  font-size: 14px;
  height: 28px;
}

.seven-btn-primary {
  background-color: #1677ff;
  color: white;
  box-shadow: 0 2px 0 rgba(5, 145, 255, 0.1);
}

.seven-btn-primary:hover {
  background-color: #4096ff;
}

.seven-btn-outline {
  background: transparent;
  border: 1px solid #1677ff;
  color: #1677ff;
}

.seven-btn-outline:hover {
  color: #4096ff;
  border-color: #4096ff;
}

.seven-btn-danger {
  background-color: #ff4d4f;
  color: white;
  box-shadow: 0 2px 0 rgba(255, 77, 79, 0.1);
}

/* 响应式调整 */
@media (max-width: 992px) {
  .seven-flex-container {
    flex-direction: column;
    height: auto;
  }

  .seven-ticket-info-grid {
    grid-template-columns: 1fr;
  }
}
/* 评论布局优化 */
.seven-comment {
  position: relative;
  padding: 12px 0;
}

.seven-comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.seven-comment-time {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 8px;
}

.seven-comment-text {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.7;
  padding-right: 40px;
}

/* 回复样式 */
.seven-comment-reply {
  margin-top: 12px;
  padding-left: 12px;
  border-left: 2px solid #1677ff;
}

/* .seven-reply-btn {
  padding: 2px 8px;
  font-size: 12px;
  background: #f0f0f0;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  color: #666;
  position: absolute;
  top: 12px;
  right: 0;
}

.seven-reply-btn:hover {
  background: #e6e6e6;
} */

/* 固定评论框 */
.seven-card-body {
  display: flex;
  flex-direction: column;
  height: 100%;
}

#seven-comments-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.seven-comment-form {
  margin-top: auto;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  padding-top: 16px;
}

.seven-padding2{
  padding: 0px 3px;
}

.seven-tabs {
  height: 100%; /* 继承父容器高度 */
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.seven-tabs-nav {
  display: flex;
  margin: 0;
  padding: 0 16px;
  background: #fafafa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  list-style: none;
}

.seven-tabs-tab {
  padding: 12px 0;
  margin-right: 24px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  position: relative;
  transition: color 0.3s;
}

.seven-tabs-tab:hover {
  color: rgba(0, 0, 0, 0.85);
}

.seven-tabs-tab-active {
  color: #1677ff;
  font-weight: 500;
}

.seven-tabs-tab-active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: #1677ff;
}

.seven-tabs-content {
  flex: 1;
  overflow: hidden;
  display: flex;
}

.change-log-pane {
  padding: 16px;
}

.seven-tabs-pane {
  flex: 1;
  display: none;
  overflow-y: auto;
  padding: 0;
  overflow: hidden
}

.seven-tabs-pane-active {
  /* display: block; */
  display: flex; /* 改为flex布局 */
  flex-direction: column;
}

/* 调整时间线位置 */
.seven-timeline {
  padding: 8px 0 0 0;
}

/* 评论面板 */
.comment-pane {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.comment-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 16px;
  height: 100%;
}

.seven-comment-actions {
  display: flex;
  gap: 8px;
  margin-left: 8px;
}

.seven-comment-action {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.seven-comment-action:hover {
  color: #1677ff;
}

.seven-comment-action svg {
  width: 14px;
  height: 14px;
}

.seven-comment-reply-to {
  color: #9E9E9E;
  font-size: 12px;
  margin-bottom: 4px;
}


.seven-comment-text {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.7;
}

.seven-reply-indicator {
  display: none;
  margin-bottom: 8px;
  padding: 6px 12px;
  background: #f9f9f9;
  border-radius: 4px;
  font-size: 13px;
  color: #1677ff;
  position: relative;
}

.seven-reply-indicator::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #1677ff;
  border-radius: 2px;
}

#seven-cancel-reply {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 12px;
}

#seven-cancel-reply:hover {
  color: #1677ff;
}

#seven-comment-input {
  width: 100%;
  height: 80px;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  resize: vertical;
  margin-bottom: 12px;
  font-size: 14px;
  transition: all 0.3s;
}

#seven-comment-input:focus {
  outline: none;
  border-color: #1677ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.comment-submit-container {
  display: flex;
  justify-content: flex-end;
}

#seven-submit-comment {
  padding: 6px 16px;
  background-color: #1677ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

#seven-submit-comment:hover {
  background-color: #4096ff;
}

.seven-detail-panel, .seven-side-panel {
  height: 100%; /* 继承父容器高度 */
}

.seven-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%; /* 填充整个容器高度 */
  /* padding: 40px; */
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
}

.seven-change-log-pane {
  display: flex;
  flex-direction: column;
  height: 100%; /* 继承父容器高度 */
}

/* 处理弹窗层级问题 */
.ck-body-wrapper .ck-block-toolbar-button{
  z-index: 99999999999 !important;
}

.ck-body-wrapper .ck-balloon-panel_with-arrow{
  z-index: 99999999999 !important;
}

.ck-body-wrapper .ck-mention-balloon{
  z-index: 99999999999 !important;
}

/* 评论表格样式重置 begin */
/* =============== 基础重置 =============== */
.seven-comment-text {
    /* 隔离内容区域 */
    isolation: isolate;
    contain: content;
    z-index: 100;
    position: relative;

    /* 文本样式重置 */
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
    color: #333 !important;
}

/* =============== 表格样式重置 =============== */
.seven-comment-text table {
    border-collapse: collapse !important;
    width: 100% !important;
    margin: 1.5em 0 !important;
    border: 1px solid #e0e0e0 !important;
    background-color: #fff !important;
    box-sizing: content-box !important;
}

.seven-comment-text table td,
.seven-comment-text table th {
    border: 1px solid #e0e0e0 !important;
    padding: 10px 15px !important;
    text-align: left !important;
    background-color: #fff !important;
    vertical-align: top !important;
    box-sizing: content-box !important;
}

.seven-comment-text table th {
    background-color: #f8f9fa !important;
    font-weight: 700 !important;
    color: #333 !important;
}

.seven-comment-text table tr:nth-child(even) {
    background-color: #f9f9f9 !important;
}

.seven-comment-text table caption {
    caption-side: bottom !important;
    padding: 10px 0 !important;
    font-style: italic !important;
    color: #666 !important;
}

/* 覆盖 LayUI 对表格的样式影响 */
.seven-comment-text .layui-table,
.seven-comment-text .layui-table td,
.seven-comment-text .layui-table th,
.seven-comment-text .layui-table tr {
    all: revert !important;
    box-sizing: content-box !important;
}

/* =============== 标题样式重置 =============== */
.seven-comment-text h1 {
    font-size: 2.2em !important;
    margin: 0.8em 0 0.5em 0 !important;
    font-weight: 700 !important;
    color: #1a2980 !important;
}

.seven-comment-text h2 {
    font-size: 1.8em !important;
    margin: 0.7em 0 0.4em 0 !important;
    font-weight: 700 !important;
    color: #1a2980 !important;
}

.seven-comment-text h3 {
    font-size: 1.5em !important;
    margin: 0.6em 0 0.4em 0 !important;
    font-weight: 700 !important;
    color: #333 !important;
}

.seven-comment-text h4 {
    font-size: 1.3em !important;
    margin: 0.5em 0 0.3em 0 !important;
    font-weight: 600 !important;
    color: #333 !important;
}

.seven-comment-text h5 {
    font-size: 1.1em !important;
    margin: 0.4em 0 0.3em 0 !important;
    font-weight: 600 !important;
    color: #555 !important;
}

.seven-comment-text h6 {
    font-size: 1em !important;
    margin: 0.4em 0 0.3em 0 !important;
    font-weight: 600 !important;
    color: #666 !important;
}

.seven-comment-text a {
    color: #1a73e8 !important;
    text-decoration: underline !important;
    transition: color 0.2s !important;
}

.seven-comment-text a:hover {
    color: #0d47a1 !important;
    text-decoration: none !important;
}

.seven-comment-text strong,
.seven-comment-text b {
    font-weight: 700 !important;
}

.seven-comment-text em,
.seven-comment-text i {
    font-style: italic !important;
}

.seven-comment-text u {
    text-decoration: underline !important;
}

.seven-comment-text s,
.seven-comment-text del {
    text-decoration: line-through !important;
}

.seven-comment-text sup {
    vertical-align: super !important;
    font-size: 0.8em !important;
}

.seven-comment-text sub {
    vertical-align: sub !important;
    font-size: 0.8em !important;
}

/* =============== 列表样式重置 =============== */
.seven-comment-text ul,
.seven-comment-text ol {
    margin: 1em 0 !important;
    padding-left: 2em !important;
}

.seven-comment-text ul {
    list-style-type: disc !important;
}

.seven-comment-text ol {
    list-style-type: decimal !important;
}

.seven-comment-text li {
    margin-bottom: 0.5em !important;
    line-height: 1.5 !important;
}

.seven-comment-text ul ul,
.seven-comment-text ol ol,
.seven-comment-text ul ol,
.seven-comment-text ol ul {
    margin-top: 0.5em !important;
    margin-bottom: 0.5em !important;
}

/* =============== 引用块样式重置 =============== */
.seven-comment-text blockquote {
    border-left: 4px solid #1a2980 !important;
    margin: 1.5em 0 !important;
    padding: 0.8em 1.2em !important;
    background-color: #f8f9fa !important;
    color: #555 !important;
    font-style: italic !important;
}

.seven-comment-text blockquote p {
    margin: 0.5em 0 !important;
}

/* =============== 代码样式重置 =============== */
.seven-comment-text code {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
    background-color: #f5f7fa !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-size: 0.9em !important;
    color: #d63384 !important;
}

.seven-comment-text pre {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
    background-color: #2d2d2d !important;
    color: #f8f8f2 !important;
    padding: 1.2em !important;
    border-radius: 6px !important;
    overflow: auto !important;
    margin: 1.5em 0 !important;
    line-height: 1.5 !important;
    tab-size: 4 !important;
}

.seven-comment-text pre code {
    background: none !important;
    padding: 0 !important;
    color: inherit !important;
    font-size: 1em !important;
}

/* =============== 图片样式重置 =============== */
.seven-comment-text img {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
    margin: 1.5em auto !important;
    border-radius: 4px !important;
}

.seven-comment-text img.image-style-align-left {
    float: left !important;
    margin: 0.5em 1em 0.5em 0 !important;
}

.seven-comment-text img.image-style-align-right {
    float: right !important;
    margin: 0.5em 0 0.5em 1em !important;
}

.seven-comment-text img.image-style-align-center {
    display: block !important;
    margin: 1em auto !important;
}

.seven-comment-text figcaption {
    text-align: center !important;
    font-style: italic !important;
    color: #666 !important;
    margin-top: 0.5em !important;
    font-size: 0.9em !important;
}

/* =============== 分隔线样式重置 =============== */
.seven-comment-text hr {
    border: 0 !important;
    border-top: 1px solid #e0e0e0 !important;
    margin: 1.5em 0 !important;
}

/* =============== 其他富文本元素重置 =============== */
/* .seven-comment-text .table {
    overflow-x: auto !important;
    margin: 1.5em 0 !important;
} */

.seven-comment-text .ck-widget {
    margin: 1em 0 !important;
}

.seven-comment-text .media-embed {
    margin: 1.5em 0 !important;
}

/* =============== 响应式调整 =============== */
@media (max-width: 768px) {
    .seven-comment-text table {
        font-size: 0.9em !important;
    }

    .seven-comment-text table td,
    .seven-comment-text table th {
        padding: 8px 12px !important;
    }
}
.mention{
  font-size: 14px;
  color: #1677ff;
}
/* 评论表格样式重置 end */