/**
 * This configuration was generated using the CKEditor 5 Builder. You can modify it anytime using this link:
 * https://ckeditor.com/ckeditor-5/builder/#installation/NoFgNARATAdA7DADBSUCsU4EYAcOoCcOaWaBiBlIaIBaiAbJU1otgVChAF4AWAtAGMAdikRhgWMOPFTZAXUhYAhnABmOQSAjygA=
 */

const LICENSE_KEY =
	'eyJhbGciOiJFUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************.uH3rwbvHAaCvWBifVNZ_lFijiGfWibTdzLmoegEa08RFDUTfuPvAHvrsy-uAjhHhMuAUxXf2Mia44aGdXJy_4A';

function commentCkeditor(id, options = {}) {
	const initialUsers = options.initialMentionUsers || [];
	let lastQuery = '';
  let lastResults = [];

	console.log(initialUsers)
	function debounce(func, wait) {
    let timeout;
    return function(...args) {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

	function getMentionFeed(query) {
    return new Promise(resolve => {
      // 保存当前查询状态
      lastQuery = query;

      // 1. 如果查询为空（只输入了@），显示初始用户或默认用户
      if (query.length === 0) {
        // 优先显示初始用户
        if (initialUsers.length > 0) {
          return resolve(initialUsers);
        }

        // 如果没有初始用户，获取默认用户列表
        return fetchDefaultUsers(resolve);
      }

      // 2. 如果有查询内容，进行搜索
      debouncedSearchUsers(query, resolve);
    });
  }

	function fetchDefaultUsers(resolve) {
    $.ajax({
      url: '/admin/public_api/mention_users',
      method: 'GET',
      dataType: 'json',
      data: { query: '' }, // 空查询获取默认用户
      success: function(data) {
        // 缓存结果
        lastResults = data;
        resolve(data);
      },
      error: function() {
        resolve([]);
      }
    });
  }

	const debouncedSearchUsers = debounce((query, resolve) => {
    // 如果查询未改变，使用上次结果
    if (query === lastQuery && lastResults.length > 0) {
      return resolve(lastResults);
    }

    $.ajax({
      url: '/admin/public_api/mention_users',
      method: 'GET',
      dataType: 'json',
      data: { query: query },
      success: function(data) {
        // 缓存结果
        lastResults = data;
        lastQuery = query;
        resolve(data);
      },
      error: function() {
        resolve([]);
      }
    });
  }, 5);

	const {
		ClassicEditor,
		Alignment,
		Autoformat,
		AutoImage,
		AutoLink,
		Autosave,
		Bold,
		CloudServices,
		Code,
		CodeBlock,
		BalloonToolbar,
		Emoji,
		Essentials,
		FontBackgroundColor,
		FontColor,
		FontFamily,
		FontSize,
		GeneralHtmlSupport,
		Heading,
		Highlight,
		HtmlComment,
		HtmlEmbed,
		ImageBlock,
		ImageCaption,
		ImageInline,
		ImageInsertViaUrl,
		ImageResize,
		ImageStyle,
		ImageTextAlternative,
		ImageToolbar,
		ImageUpload,
		Indent,
		IndentBlock,
		Italic,
		Link,
		LinkImage,
		List,
		ListProperties,
		Mention,
		Paragraph,
		ShowBlocks,
		Table,
		TableCaption,
		TableCellProperties,
		TableColumnResize,
		TableProperties,
		TableToolbar,
		TextTransformation,
		BlockToolbar,
		HeadingButtonsUI,
		ParagraphButtonUI,
		Base64UploadAdapter
	} = window.CKEDITOR;

	/**
	 * This is a 24-hour evaluation key. Create a free account to use CDN: https://portal.ckeditor.com/checkout?plan=free
	 */

	const editorConfig = {
		toolbar: {
			items: [
				'undo',
				'redo',
				'|',
				'showBlocks',
				'|',
				'heading',
				'|',
				'code',
				'|',
				'emoji',
				'link',
				'insertTable',
				'codeBlock',
				// 'htmlEmbed',
				'|',
				'imageUpload'
			],
			shouldNotGroupWhenFull: false
		},
		balloonToolbar: ['fontSize', 'fontFamily', 'fontColor', 'fontBackgroundColor', '|', 'code', 'bold', 'italic', 'highlight'],
		blockToolbar: [
			'fontSize', 'fontColor', 'fontBackgroundColor','bold', 'italic', 'highlight', 'imageUpload', '|', 'alignment', 'bulletedList', 'numberedList'
		],
		plugins: [
			Alignment,
			Autoformat,
			AutoImage,
			AutoLink,
			Autosave,
			Bold,
			CloudServices,
			BalloonToolbar,
			Code,
			Emoji,
			CodeBlock,
			Essentials,
			FontBackgroundColor,
			FontColor,
			FontFamily,
			FontSize,
			GeneralHtmlSupport,
			Heading,
			Highlight,
			HtmlComment,
			HtmlEmbed,
			ImageBlock,
			ImageCaption,
			ImageInline,
			ImageInsertViaUrl,
			ImageResize,
			ImageStyle,
			ImageTextAlternative,
			ImageToolbar,
			ImageUpload,
			Indent,
			IndentBlock,
			Italic,
			Link,
			LinkImage,
			List,
			ListProperties,
			Mention,
			Paragraph,
			ShowBlocks,
			Table,
			TableCaption,
			TableCellProperties,
			TableColumnResize,
			TableProperties,
			TableToolbar,
			TextTransformation,
			BlockToolbar,
			ParagraphButtonUI,
			HeadingButtonsUI,
			Base64UploadAdapter,
			MentionCustomization
		],
		fontFamily: {
			supportAllValues: true
		},
		fontSize: {
			options: [10, 12, 14, 'default', 18, 20, 22],
			supportAllValues: true
		},
		heading: {
			options: [
				{
					model: 'paragraph',
					title: 'Paragraph',
					class: 'ck-heading_paragraph'
				},
				{
					model: 'heading1',
					view: 'h1',
					title: 'Heading 1',
					class: 'ck-heading_heading1'
				},
				{
					model: 'heading2',
					view: 'h2',
					title: 'Heading 2',
					class: 'ck-heading_heading2'
				},
				{
					model: 'heading3',
					view: 'h3',
					title: 'Heading 3',
					class: 'ck-heading_heading3'
				},
				{
					model: 'heading4',
					view: 'h4',
					title: 'Heading 4',
					class: 'ck-heading_heading4'
				},
				{
					model: 'heading5',
					view: 'h5',
					title: 'Heading 5',
					class: 'ck-heading_heading5'
				},
				{
					model: 'heading6',
					view: 'h6',
					title: 'Heading 6',
					class: 'ck-heading_heading6'
				}
			]
		},
		htmlSupport: {
			allow: [
				{
					name: /^.*$/,
					styles: true,
					attributes: true,
					classes: true
				}
			]
		},
		image: {
			toolbar: [
				'toggleImageCaption',
				'imageTextAlternative',
				'|',
				'imageStyle:inline',
				'imageStyle:wrapText',
				'imageStyle:breakText',
				'|',
				'resizeImage'
			]
		},
		language: 'zh-cn',
		licenseKey: 'GPL',
		link: {
			addTargetToExternalLinks: true,
			defaultProtocol: 'https://',
			decorators: {
				toggleDownloadable: {
					mode: 'manual',
					label: 'Downloadable',
					attributes: {
						download: 'file'
					}
				}
			}
		},
		list: {
			properties: {
				styles: true,
				startIndex: true,
				reversed: true
			}
		},
		mention: {
			feeds: [
				{
					marker: '@',
					trigger: '@', // 自定义触发字符
          pattern: /(?:^|\s)@(\w*)$/,// 自定义匹配规则
					feed: getMentionFeed,
					minimumCharacters: 0 // 触发搜索的最小字符数
				}
			]
		},
		placeholder: '请输入您的内容...',
		table: {
			contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells', 'tableProperties', 'tableCellProperties']
		}
	};

	return ClassicEditor.create(document.querySelector(id), editorConfig).then(editor => {
		// 可以在这里对编辑器进行额外的配置或监听事件
		return editor; // 这个 return 是给 .then 链的
	})
	.catch(error => {
		console.error('There was a problem initializing the editor.', error);
	});

}


// @ 之后 添加用户ID
function MentionCustomization( editor ) {
	// The upcast converter will convert <span class="mention" href="" data-user-id="">
	// elements to the model 'mention' attribute.
	editor.conversion.for( 'upcast' ).elementToAttribute( {
		view: {
			name: 'span',
			key: 'data-mention',
			classes: 'mention',
			attributes: {
				href: false,
				'data-user-id': true
			}
		},
		model: {
			key: 'mention',
			value: viewItem => {
				// The mention feature expects that the mention attribute value
				// in the model is a plain object with a set of additional attributes.
				// In order to create a proper object, use the toMentionAttribute helper method:
				const mentionAttribute = editor.plugins.get( 'Mention' ).toMentionAttribute( viewItem, {
					// Add any other properties that you need.
					userId: viewItem.getAttribute( 'data-user-id' )
				} );

				return mentionAttribute;
			}
		},
		converterPriority: 'high'
	} );

	// Downcast the model 'mention' text attribute to a view <a> element.
	editor.conversion.for( 'downcast' ).attributeToElement( {
		model: 'mention',
		view: ( modelAttributeValue, { writer } ) => {
			// Do not convert empty attributes (lack of value means no mention).
			if ( !modelAttributeValue ) {
				return;
			}

			return writer.createAttributeElement( 'span', {
				class: 'mention',
				'data-mention': modelAttributeValue.id,
				'data-user-id': modelAttributeValue.userId
			}, {
				// Make mention attribute to be wrapped by other attribute elements.
				priority: 20,
				// Prevent merging mentions together.
				id: modelAttributeValue.uid
			} );
		},
		converterPriority: 'high'
	} );
}

// 判断内容是否为空的辅助函数
function isContentEmpty(htmlContent) {
	// 创建一个临时的 DOM 元素来解析 HTML
	const tempDiv = document.createElement('div');
	tempDiv.innerHTML = htmlContent;

	// 获取纯文本内容 (会自动忽略 HTML 标签)
	const textContent = tempDiv.textContent || tempDiv.innerText || '';

	// 去除首尾空白字符（包括空格、换行、制表符等）
	const trimmedText = textContent.trim();

	// 如果去除空白后的文本长度为 0，则认为内容为空
	return trimmedText.length === 0;
}