class Admin::OrganizationsController < Admin::ApplicationController
  before_action do
    authorize Organization
  end

  before_action :set_organization, only: [:show, :edit, :update, :destroy, :permission_controllers, :assign_permission_controller, :remove_permission_controller, :users]

  # GET /admin/organizations
  def index
    @q = Organization.order(created_at: :desc).ransack(params[:q])
    @organizations = @q.result
    if request.xhr?
      @result = @organizations.page(params[:page]).per(params[:limit]).map do |organization|
        {
          id: organization.id,
          name: organization.name,
          org_type: organization.org_type_i18n,
          parent_id: organization.parent&.name,
          assigned_permission_controllers_count: organization.assigned_permission_controllers.count
        }
      end
      render json: {code: 0, data: @result, count: @organizations.count}
    end
  end

  # GET /admin/organizations/1
  def show
  end

  # GET /admin/organizations/new
  def new
    @organization = Organization.new
  end

  # GET /admin/organizations/1/edit
  def edit
  end

  # POST /admin/organizations
  def create
    @organization = Organization.new(organization_params)
    begin
      @status = true
      ActiveRecord::Base.transaction do
        @organization.save!
        @organization.users.create!(
          name: params[:admin_user][:name],
          phone: params[:admin_user][:phone],
          password: params[:admin_user][:password],
          password_confirmation: params[:admin_user][:password],
          is_admin: true
        )
        @organization.generate_admin_auth
        # 初始化项目权限配置
        ProjectPermissionConfig.generate_permission(@organization.id)
      end
    rescue => exception
      @status = false
      Rails.logger.info exception.message
      @msg = "失败了, #{exception.message}"
    end

  end

  # PATCH/PUT /admin/organizations/1
  def update
    if @organization.update(organization_params)
      @status = true
    else
      @status = false
      @msg = "失败了, #{@organization.errors.full_messages.join(',')}"
    end
  end

  # DELETE /admin/organizations/1
  def destroy
    if @organization.destroy
      @status = true
    else
      @status = false
      @msg = "失败了, #{@organization.errors.full_messages.join(',')}"
    end
  end

  # GET /admin/organizations/1/permission_controllers
  def permission_controllers
    @available_permission_controllers = @organization.available_permission_controllers
    @assigned_permission_controllers = @organization.assigned_permission_controllers
  end

  # POST /admin/organizations/1/assign_permission_controller
  def assign_permission_controller
    if @organization.assign_permission_controller(params[:permission_controller_id])
      @status = true
      @msg = "模块分配成功"
    else
      @status = false
      @msg = "模块分配失败"
    end
    @available_permission_controllers = @organization.available_permission_controllers
    @assigned_permission_controllers = @organization.assigned_permission_controllers

    # respond_to do |format|
    #   format.json { render json: { status: @status, msg: @msg } }
    #   format.js
    # end
  end

  # DELETE /admin/organizations/1/remove_permission_controller
  def remove_permission_controller
    @organization.remove_permission_controller(params[:permission_controller_id])
    @status = true
    @msg = "模块移除成功"
    @available_permission_controllers = @organization.available_permission_controllers
    @assigned_permission_controllers = @organization.assigned_permission_controllers

    # respond_to do |format|
    #   format.json { render json: { status: @status, msg: @msg } }
    #   format.js
    # end
  end

  def users
    if params[:open_status].blank?

      @users = @organization.users.ransack({name_or_phone_cont: params[:name_or_phone_cont]}).result.order(created_at: :desc)
      if request.xhr?
        result = @users.page(params[:page]).per(params[:limit]).map do |user|
          {
            id: user.id,
            name: user.name,
            avatar: user.avatar_url,
            phone: user.phone,
            recommend_user_name: user.recommend_user&.name,
            created_at: user.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            status: user.status
          }
        end
        render json: {code: 0, msg: "", count: @users.count, data: result}
      end
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_organization
      @organization = Organization.find(params[:id])
    end

    # Only allow a trusted parameter "white list" through.
    def organization_params
      params.require(:organization).permit(:name, :org_type, :parent_id)
    end
end
