class Admin::WorkOrdersController < Admin::ApplicationController
  before_action do
    authorize WorkOrder
  end
  before_action :set_project
  before_action :set_work_order, only: [:show, :edit, :update, :destroy, :change_state, :details]

  # GET /admin/work_orders
  def index
    # 导出
    if params[:excel]
      if params[:excel_tag] == 'bug'
        file = WorkOrder.export_excel_bug(@project)
        filename = "#{@project.name}-工单bug.xlsx"
      else
        file = WorkOrder.export_excel_demand(@project)
        filename = "#{@project.name}-工单需求.xlsx"
      end
      send_file file, filename: filename
      return
    end
    if request.xhr?
      if params[:page_tag] == "my_work_orders"
        @q = WorkOrder.where_current_user(current_user).order(created_at: :desc).ransack(params[:q])
        @work_orders = @q.result.page(params[:page]).per(params[:limit])
      else
        @q = WorkOrder.order(created_at: :desc).ransack(params[:q])
        @work_orders = @q.result.page(params[:page]).per(params[:limit])
      end
      @result = @work_orders.map do |work_order|
        {
          id: work_order.id,
          aasm_state: t("workflows.work_order.aasm_state.#{work_order.aasm_state}"),
          display_button: work_order.is_display_button(current_user),
          customer_name: work_order.customer_name,
          demand_sources: work_order.demand_sources_i18n,
          description: work_order.description,
          ended_at: work_order.ended_at&.strftime('%F %T'),
          file: work_order.file,
          hardware_version: work_order.hardware_version,
          is_platform_commonality: work_order.is_platform_commonality ? t("workflows.work_order.is_platform_commonality.#{work_order.is_platform_commonality}") : nil,
          priority: work_order.priority_color,
          problem_severity: work_order.problem_severity_i18n,
          product_name: work_order.product_name,
          project_progress: work_order.project_progress_i18n,
          repro_steps: work_order.repro_steps,
          started_at: work_order.started_at&.strftime('%F %T'),
          title: work_order.title,
          which_module: work_order.which_module,
          work_type: work_order.work_type_i18n,
          created_at: work_order.created_at.strftime('%F %T'),
          chip_config_id: work_order.chip_config.name,
          chip_os_software_id: work_order.chip_os_software.name,
          chip_os_version_id: work_order.chip_os_version&.version,
          founder_id: work_order.founder.name,
          obligation_user_id: work_order.obligation_user&.name,
          product_category_id: work_order.product_category.name,
          receiver_user_id: work_order.receiver_user&.name,
          project_name: work_order.project.name,
          project_id: work_order.project_id,
          tab: work_order.class.to_s.underscore.pluralize,
          button: work_order.get_button(current_user),
          daily_status_tag: work_order.daily_status_tag,
          around_time: "#{work_order.started_at.strftime("%F %T")} ~ #{work_order.ended_at.strftime("%F %T")}",
          edit_delete_status: work_order.edit_delete_status(current_user)
        }
      end
      render json: {code: 0, msg: 'success', data: @result, count: @work_orders.count}
    end
  end

  def project_bug_works
    @is_export = current_user.is_authority('export_work_order')
  end

  def project_demand_works
    @is_export = current_user.is_authority('export_work_order')
  end

  # GET /admin/work_orders/1
  def show
    @approval_flow = @work_order.current_approval_flow
    @comment_tree = @work_order.comments_json
    @commentable_type = 'WorkOrder'
    @commentable_id = @work_order.id
    @initial_mention_users = @work_order.project.mentioned_users
  end

  def details
    show
  end

  # GET /admin/work_orders/new
  def new
    @work_order = WorkOrder.new(project_id: @project.id)
    @chip_config_array = ChipConfig.where(organization_id: @project.chip_organization_id).map{|chip_config| {name: chip_config.name, value: chip_config.id}}
    @chip_os_software_array = []
    @chip_os_version_array = []
    @tag = params[:tag]
    @event_tag = params[:event_tag]
    @product_category_array = ProductCategory.where(organization_id: current_organization_id).pluck(:name, :id)
  end

  # GET /admin/work_orders/1/edit
  def edit
    @product_category_array = ProductCategory.where(organization_id: @project.chip_organization_id ).pluck(:name, :id)
    @chip_config_array = ChipConfig.where(organization_id: @project.chip_organization_id).map{|chip_config| {name: chip_config.name, value: chip_config.id, selected: @work_order.chip_config_id == chip_config.id}}
    @chip_os_software_array = ChipOsSoftware.where(chip_config_id: @work_order.chip_config_id, organization_id: @project.chip_organization_id).map{|chip_os_software| {name: chip_os_software.name, value: chip_os_software.id, selected: @work_order.chip_os_software_id == chip_os_software.id}}
    @chip_os_version_array = ChipOsVersion.where(chip_os_software_id: @work_order.chip_os_software_id, organization_id: @project.chip_organization_id).map{|chip_os_version| {name: chip_os_version.version, value: chip_os_version.id, selected: @work_order.chip_os_version_id == chip_os_version.id}}
    @tag = params[:tag]
    @event_tag = params[:event_tag]
  end

  # POST /admin/work_orders
  def create
    ActiveRecord::Base.transaction do
      @work_order = WorkOrder.new(work_order_params.merge(aasm_state: 'not_started', founder_id: current_user.id, founder_email: current_user.email, founder_phone: current_user.phone))
      @status = @work_order.save!
      project_risk = ProjectRisk.find_by(id: work_order_params[:project_risk_id])
      if project_risk.present?
        project_risk.update!(riskable: @work_order, aasm_state: 'converted')
      end
    end
  end

  # PATCH/PUT /admin/work_orders/1
  def update
    button_tag = work_order_params[:button_tag]
    @status = @work_order.update(work_order_params)
    case @work_order.aasm_state
    when 'not_started'
      @work_order.evaluate! if @work_order.receiver_user_id.present? && button_tag == "receiver" #点击的指定评估人按钮
    when 'evaluating'
      @work_order.in_progress! if @work_order.obligation_user_id.present? && button_tag == "appoint" #受理，指定责任人
      @work_order.closed! if button_tag == "unacceptance" #不受理，驳回
    when 'in_progress'
      @work_order.solved! if button_tag == "solved" #责任人解决
    when 'solved'
      @work_order.in_progress! if button_tag == "unsolved" #未解决
    when 'close'
      @work_order.reopened!
    end
    @comment_tree = @work_order.comments_json
  end

  # DELETE /admin/work_orders/1
  def destroy
    @status = @work_order.destroy
  end

  #评估人能看见的工单
  def evaluate_work_orders
    if request.xhr?
      @work_orders = WorkOrder.where_current_user(current_user).where(aasm_state: ["evaluating", "reopen"]).order(created_at: :desc)
      @result = @work_orders.page(params[:page]).per(params[:limit]).map do |work_order|
        {
          id: work_order.id,
          aasm_state: t("workflows.work_order.aasm_state.#{work_order.aasm_state}"),
          display_button: work_order.is_display_button(current_user),
          customer_name: work_order.customer_name,
          demand_sources: work_order.demand_sources_i18n,
          description: work_order.description,
          ended_at: work_order.ended_at&.strftime("%Y-%m-%d %H:%M:%S"),
          file: work_order.file,
          founder_email: work_order.founder_email,
          founder_phone: work_order.founder_phone,
          hardware_version: work_order.hardware_version,
          is_platform_commonality: work_order.is_platform_commonality ? t("workflows.work_order.is_platform_commonality.#{work_order.is_platform_commonality}") : nil,
          priority: work_order.priority_i18n,
          problem_severity: work_order.problem_severity_i18n,
          product_name: work_order.product_name,
          project_progress: work_order.project_progress_i18n,
          repro_steps: work_order.repro_steps,
          started_at: work_order.started_at&.strftime("%Y-%m-%d %H:%M:%S"),
          title: work_order.title,
          which_module: work_order.which_module,
          work_type: work_order.work_type_i18n,
          # workable_type: work_order.workable_type,
          created_at: work_order.created_at,
          updated_at: work_order.updated_at,
          chip_config_id: work_order.chip_config_id,
          chip_os_software_id: work_order.chip_os_software_id,
          chip_os_version_id: work_order.chip_os_version_id,
          founder_id: work_order.founder_id,
          product_category_id: work_order.product_category_id,
          receiver_user_id: work_order.receiver_user_id,
          project_name: work_order.project.name,
          # workable_id: work_order.workable_id
        }
      end
      render json: {code: 0, msg: 'success', data: @result, count: @work_orders.count}
    end
  end

  #责任人能看见的工单
  def passing_work_orders
    if request.xhr?
      @work_orders = WorkOrder.where_current_user(current_user).where(aasm_state: "in_progress").order(created_at: :desc)
      @result = @work_orders.page(params[:page]).per(params[:limit]).map do |work_order|
        {
          id: work_order.id,
          aasm_state: t("workflows.work_order.aasm_state.#{work_order.aasm_state}"),
          display_button: work_order.is_display_button(current_user),
          customer_name: work_order.customer_name,
          demand_sources: work_order.demand_sources_i18n,
          description: work_order.description,
          ended_at: work_order.ended_at&.strftime("%Y-%m-%d %H:%M:%S"),
          file: work_order.file,
          founder_email: work_order.founder_email,
          founder_phone: work_order.founder_phone,
          hardware_version: work_order.hardware_version,
          is_platform_commonality: work_order.is_platform_commonality ? t("workflows.work_order.is_platform_commonality.#{work_order.is_platform_commonality}") : nil,
          priority: work_order.priority_i18n,
          problem_severity: work_order.problem_severity_i18n,
          product_name: work_order.product_name,
          project_progress: work_order.project_progress_i18n,
          repro_steps: work_order.repro_steps,
          started_at: work_order.started_at&.strftime("%Y-%m-%d %H:%M:%S"),
          title: work_order.title,
          which_module: work_order.which_module,
          work_type: work_order.work_type_i18n,
          # workable_type: work_order.workable_type,
          created_at: work_order.created_at,
          updated_at: work_order.updated_at,
          chip_config_id: work_order.chip_config_id,
          chip_os_software_id: work_order.chip_os_software_id,
          chip_os_version_id: work_order.chip_os_version_id,
          founder_id: work_order.founder_id,
          product_category_id: work_order.product_category_id,
          receiver_user_id: work_order.receiver_user_id,
          project_name: work_order.project.name,
          # workable_id: work_order.workable_id
        }
      end
      render json: {code: 0, msg: 'success', data: @result, count: @work_orders.count}
    end
  end

  #改变状态
  def change_state
    case params[:tag]
    when "closed"
      @work_order.closed!
    end
    @status = true
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_work_order
      @work_order = WorkOrder.find(params[:id])
    end

    def set_project
      @project = current_organization.projects.find_by(id: params[:project_id])
    end

    # Only allow a trusted parameter "white list" through.
    def work_order_params
      params.require(:work_order).except(*request.path_parameters.keys).permit(:project_id, :project_risk_id, :obligation_user_id, :aasm_state, :work_type, :title, :project_progress, :description, :customer_name, :priority, :product_name, :hardware_version, :file, :receiver_user_id, :which_module, :problem_severity, :is_platform_commonality, :chip_config_id, :chip_os_software_id, :chip_os_version_id, :product_category_id, :demand_sources, :repro_steps, :started_at, :ended_at, :founder_id, :founder_email, :founder_phone, :work_order_progress, :rejection_reason, :solution, :customer_confirmation, :button_tag)
    end
end
