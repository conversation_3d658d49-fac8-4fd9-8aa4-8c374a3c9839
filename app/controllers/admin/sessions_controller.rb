class Admin::SessionsController < ApplicationController
  # 登陆页
  def index
  end

  # 注册创建
  def create
  end

  # 验证登陆页
  def login_user
    user = User.find_by(phone: params[:phone])
    if user && user.authenticate(params[:password])
      if user.freeze?
        flash[:error] = '账户已被冻结'
        redirect_to admin_sessions_path(phone: params[:phone])
        return
      end
      # 用户登录成功
      flash[:success] = '登陆成功'
      session[:user_id] = user.id
      redirect_to admin_path
    else
      flash[:error] = '密码错误'
      redirect_to admin_sessions_path(phone: params[:phone])
    end
  end

  def logout
    if session[:oauth2_user_id].present? && session[:oauth2_access_token].present?
      oauth2_service = Oauth2Service.new
      logout_result = oauth2_service.logout(session[:oauth2_user_id], session[:oauth2_access_token])

      unless logout_result[:success]
        Rails.logger.warn "OAuth2退出登录失败: #{logout_result[:error]}"
      end

      session[:oauth2_access_token] = nil
      session[:oauth2_user_id] = nil
    end

    flash[:success] = '注销成功'
    session[:user_id] = nil if session[:user_id].present?
    redirect_to admin_sessions_path
  end

  # 更新用户密码
  def update_password
    begin
      @status = true
      current_user = User.find_by(id: session[:user_id])
      raise '密码不正确' unless current_user.authenticate(params[:password])
      raise '新密码与确认密码不一致！' if params[:new_password] != params[:new_password_confirmation]
      current_user.password = params[:new_password]
      current_user.password_confirmation = params[:new_password_confirmation]
      current_user.save!
      flash[:success] = '密码修改成功！请重新登陆'
      session[:user_id] = nil
      render json: {status: true}
    rescue => exception
      render json: {status: false, msg: "失败了, #{exception.message}"}
    end

  end


  private
  def sign_params
    params.permit(:phone, :password)
  end
end
