class Oauth2SessionsController < ApplicationController
  skip_before_action :verify_authenticity_token, only: [:callback]

  def login
    oauth2_service = Oauth2Service.new
    authorization_url = oauth2_service.authorization_url

    redirect_to authorization_url, allow_other_host: true
  end

  def callback
    begin
      if params[:error].present?
        handle_oauth2_error("授权失败: #{params[:error_description] || params[:error]}")
        return
      end

      auth_code = params[:code]

      if auth_code.blank?
        handle_oauth2_error('授权失败，未获取到授权码')
        return
      end

      oauth2_service = Oauth2Service.new

      token_result = oauth2_service.get_access_token(auth_code)

      unless token_result[:success]
        handle_oauth2_error("获取访问令牌失败: #{token_result[:error]}")
        return
      end

      access_token = token_result[:data]['access_token']

      user_info_result = oauth2_service.get_user_info(access_token)

      unless user_info_result[:success]
        handle_oauth2_error("获取用户信息失败: #{user_info_result[:error]}")
        return
      end

      user_info = user_info_result[:data]

      if user_info['phone'].blank?
        handle_oauth2_error('用户信息中缺少手机号，无法完成登录')
        return
      end

      user = User.find_by(phone: user_info['phone'])
      unless user
        handle_oauth2_error("用户不存在，请联系管理员")
        return
      end

      if user.freeze?
        handle_oauth2_error('账户已被冻结，请联系管理员')
        return
      end

      session[:user_id] = user.id
      session[:oauth2_access_token] = access_token
      session[:oauth2_user_id] = user_info['id']

      user.update(last_login_at: Time.current)

      log_oauth2_login(user, user_info)

      flash[:success] = 'OAuth2登录成功'
      redirect_to admin_path
    rescue => e
      Rails.logger.error "OAuth2回调处理异常: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      handle_oauth2_error('登录过程中发生异常，请稍后重试')
    end
  end

  private

    def log_oauth2_login(user, user_info, action = 'login')
      Rails.logger.info "OAuth2登录成功: 用户ID=#{user.id}, 手机号=#{user.phone}, OAuth2用户ID=#{user_info['id']}, 操作=#{action}"
    end

    def handle_oauth2_error(error_message)
      Rails.logger.error "OAuth2错误: #{error_message}"
      Rails.logger.error "请求参数: #{params.inspect}"
      Rails.logger.error "Session信息: user_id=#{session[:user_id]}, oauth2_user_id=#{session[:oauth2_user_id]}"

      flash[:error] = error_message
      redirect_to admin_sessions_path
    end
end
