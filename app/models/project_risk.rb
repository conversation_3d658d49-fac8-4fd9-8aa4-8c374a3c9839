# == Schema Information
#
# Table name: project_risks
#
#  id                           :bigint           not null, primary key
#  aasm_state(状态)             :integer
#  act_ended_at(时间结束时间)   :datetime
#  act_started_at(实际起始时间) :datetime
#  action_plan(行动计划)        :text
#  close_reason(关闭原因)       :string
#  coping_strategy(应对策略)    :integer
#  deleted_at(删除时间)         :datetime
#  description(风险描述)        :string
#  ended_at(结束时间)           :datetime
#  name(风险名称)               :string
#  nature(风险性质)             :integer
#  problem_severity(影响程度)   :integer
#  risk_level(风险等级)         :integer
#  risk_type(风险类型)          :integer
#  riskable_type                :string
#  started_at(开始时间)         :datetime
#  which_module(所属模块)       :string
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  obligation_user_id(责任人ID) :integer
#  project_id(项目ID)           :integer
#  riskable_id(多态)            :bigint
#  user_id(创建人ID)            :integer
#
# Indexes
#
#  index_project_risks_on_obligation_user_id  (obligation_user_id)
#  index_project_risks_on_project_id          (project_id)
#  index_project_risks_on_riskable            (riskable_type,riskable_id)
#  index_project_risks_on_user_id             (user_id)
#
class ProjectRisk < ApplicationRecord
  include Commentable

  belongs_to :riskable, polymorphic: true, optional: true
  belongs_to :project
  belongs_to :user
  belongs_to :obligation_user, class_name: 'User', foreign_key: :obligation_user_id, optional: true # 责任人

  has_paper_trail ignore: [:updated_at]

  acts_as_paranoid

  #风险等级： 高风险 、 中风险 、低风险
  enum risk_level: {high_risk: 1, medium_risk: 2, low_risk: 3}
  #风险性质：威胁 、 潜在机会
  enum nature: [:threat, :opportunity]
  #应对策略：避免、防范、减缓、研究、保留、转移
  enum coping_strategy: {avoid: 1, prevent: 2, mitigate: 3, research: 4, reserve: 5, transfer: 6}
  #状态: 已识别 、 已跟踪 、 已转化 、 已关闭
  enum aasm_state: {identified: 1, tracked: 2, converted: 3, closed: 4}
  #风险类型,商业风险 进度风险 技术风险  资源风险
  enum risk_type: {commercial_risk: 1, schedule_risk: 2, technical_risk: 3, resource_risk: 4}
  # 影响程度
  enum problem_severity: {p_one: 1, p_two: 2, p_three: 3, p_four: 4, p_five: 5}
  attr_accessor :button_tag

  def self.ransackable_attributes(auth_object = nil)
    ["aasm_state", "act_ended_at", "act_started_at", "action_plan", "category", "close_reason", "coping_strategy", "created_at", "description", "ended_at", "id", "name", "nature", "organization_id", "problem_severity", "project_id", "risk_level", "risk_type", "riskable_id", "riskable_type", "started_at", "updated_at", "which_module"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["comments", "obligation_user", "project", "riskable", "user", "versions"]
  end

  def is_display_button(current_user)
    arr = []
    case self.aasm_state
    when 'identified'
      arr += ["edit", "appoint", "closed"] if current_user.id == self.user_id #客户
      arr += [] if current_user.id == self.obligation_user_id #责任人
    when 'tracked'
      arr += ["closed"] if current_user.id == self.user_id #客户
      arr += ["converted"] if current_user.id == self.obligation_user_id #责任人
    else
    end
    arr
  end

  def daily_status_tag
    return [] unless (self.identified? || self.tracked?)
    if self.ended_at < Time.now
      return ['已延期', "seven-tag-red"]
    elsif (self.ended_at - 1.days) < Time.now
      return ['即将延期', "seven-tag-yellow"]
    else
      []
    end
  end

  # 导出项目风险
  def self.export_excel(project)
    header = ['ID', '风险名称', '风险类型', '描述', '问题严重性', '所属模块', '风险等级', '风险性质', '影响程度', '应对策略', '行动计划', '状态', '开始时间', '结束时间', '创建人', '责任人']
    output = ExcelUtil.generate do |wb|
      styles = ExcelUtil.get_sheet_style(wb)
      wb.add_worksheet(name: '工单bug') do |sheet|
        sheet.add_row header, style: styles[:header]
        #导入内容
        project.project_risks.order(created_at: :desc).each do |project_risk|
          row = [project_risk.id, project_risk.name, project_risk.risk_type_i18n, strip_tags(project_risk.description), project_risk.which_module, project_risk.risk_level_i18n, project_risk.nature_i18n, project_risk.problem_severity_i18n,
                 project_risk.coping_strategy_i18n, strip_tags(project_risk.action_plan), project_risk.status_i18n, project_risk.started_at&.strftime('%F %T'), project_risk.ended_at&.strftime('%F %T'),
                 project_risk.user&.name, project_risk.obligation_user&.name]

          sheet.add_row row, types: :string, style: styles[:content]
        end
      end
    end
    output
  end

  def status_i18n
    aasm_state_i18n
  end

  def data_type_title
    "风险"
  end

  def priority_color
    case self.risk_level
    when 'high_risk'
      [self.risk_level_i18n, "seven-tag-red"]
    when 'medium_risk'
      [self.risk_level_i18n, "seven-tag-yellow"]
    when 'low_risk'
      [self.risk_level_i18n, "seven-tag-blue"]
    end
  end

  def get_button(current_user)
    button = []
    case self.aasm_state
    when 'identified'
      button += [{ title: '指派', id: 'appoint'}, { title: '关闭', id: 'closed'}] if current_user.id == self.user_id #客户
    when 'tracked'
      button << {title: '关闭', id: 'closed'} if current_user.id == self.user_id
      button << {title: '转化', id: 'converted'} if current_user.id == self.obligation_user_id
    end
    button
  end

  def build_change_log
    change_logs = []
    versions = self.versions.reorder(created_at: :desc)

    versions.each do |version|
      change_at = version.created_at.strftime("%Y-%m-%d %H:%M")
      change_by = User.find_by(id: version.whodunnit)&.name || "未知用户"
      change_type = version.event

      if version.event == "create"
        change_logs << {
          change_at: change_at,
          change_by: change_by,
          change_type: change_type,
          changes: "新建"
        }
      elsif version.event == "update"
        changeset = version.changeset.presence || {}
        formatted_changes = format_changeset(changeset)

        unless formatted_changes.empty?
          change_logs << {
            change_at: change_at,
            change_by: change_by,
            change_type: change_type,
            changes: formatted_changes
          }
        end
      elsif version.event == "destroy"
        change_logs << {
          change_at: change_at,
          change_by: change_by,
          change_type: change_type,
          changes: "已删除"
        }
      end
    end

    change_logs
  end

  def format_changeset(changeset)
    changes = []

    # 定义需要格式化为时间的字段
    time_fields = [:started_at, :ended_at, :act_started_at, :act_ended_at]

    # 定义需要翻译的字段及其对应 i18n 前缀
    translatable_fields = {
      risk_level: 'enums.project_risk.risk_level',
      nature: 'enums.project_risk.nature',
      coping_strategy: 'enums.project_risk.coping_strategy',
      aasm_state: 'enums.project_risk.aasm_state',
      risk_type: 'enums.project_risk.risk_type',
    }

    changeset.each do |field, (old_value, new_value)|
      next if ["id", "created_at", "deleted_at"].include?(field.to_s)

      field_sym = field.to_sym

      # 格式化时间字段
      if time_fields.include?(field_sym)
        old_value = Time.parse(old_value).strftime("%F %T") if old_value.present?
        new_value = Time.parse(new_value).strftime("%F %T") if new_value.present?
      end

      if field == 'obligation_user_id' || field == 'user_id'
        old_value = User.find(old_value).name if old_value.present?
        new_value = User.find(new_value).name if new_value.present?
      end

      # 国际化枚举字段
      if translatable_fields.key?(field_sym)
        old_value = old_value.presence && I18n.t("#{translatable_fields[field_sym]}.#{old_value}")
        new_value = new_value.presence && I18n.t("#{translatable_fields[field_sym]}.#{new_value}")
      end
      field = I18n.t("activerecord.attributes.project_risk.#{field}")
      changes << {
        field: field,
        from: old_value,
        to: new_value
      }
    end

    changes
  end
end
