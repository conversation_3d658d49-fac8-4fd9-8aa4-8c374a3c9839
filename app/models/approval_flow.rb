# == Schema Information
#
# Table name: approval_flows
#
#  id                                                :bigint           not null, primary key
#  deleted_at(删除时间)                              :datetime
#  flowable_type                                     :string
#  is_effect(是否生效)                               :boolean
#  status(状态(1: "进行中", 2: "已完成", 3: "驳回")) :integer
#  created_at                                        :datetime         not null
#  updated_at                                        :datetime         not null
#  current_step_id(当前步骤ID)                       :integer
#  flowable_id(多态)                                 :bigint
#  organization_flow_id(流程ID)                      :integer
#  organization_id(组织ID)                           :integer
#  user_id(用户ID、发起人ID)                         :integer
#
# Indexes
#
#  index_approval_flows_on_flowable              (flowable_type,flowable_id)
#  index_approval_flows_on_organization_flow_id  (organization_flow_id)
#  index_approval_flows_on_organization_id       (organization_id)
#  index_approval_flows_on_user_id               (user_id)
#
class ApprovalFlow < ApplicationRecord
  acts_as_paranoid

  belongs_to :flowable, polymorphic: true
  belongs_to :organization_flow
  belongs_to :organization
  belongs_to :user

  has_many :approval_steps, dependent: :destroy

  # 进行中 1, 已完成 2, 驳回 3
  enum status: {in_progress: 1, approved: 2, rejected: 3}

  after_save :change_status

  def self.build_approval(flowable, user)
    begin
      ActiveRecord::Base.transaction do
        organization_flow = nil
        case flowable.class.to_s
        when 'ProjectOrganization'
          if flowable.project.p_internal? # 内部方案 走内部自己的审核流程
            organization_flow = OrganizationFlow.find_by(flow_type: 'f_project', organization_id: user.organization_id)
          elsif flowable.project.p_support? # 技术支持案 对应的是上级配置的审核流程
            organization_flow = OrganizationFlow.find_by(flow_type: 'f_project_support', organization_id: user.organization.parent_id)
          elsif flowable.project.p_joint? # 联合开发案
            organization_flow = OrganizationFlow.find_by(flow_type: 'f_project_joint', organization_id: user.organization.parent_id)
          end
        when 'Organization'
          organization_flow = OrganizationFlow.find_by(flow_type: 'f_organization', organization_id: user.organization_id)
        end
        raise "未找到对应组织的审核流程！" unless organization_flow

        flowable.approval_flows.update_all(is_effect: false) if flowable.approval_flows.present?
        approval_flow = ApprovalFlow.create!(
          flowable: flowable,
          organization_flow_id: organization_flow.id,
          organization_id: organization_flow.organization_id,
          user_id: user.id,
          status: 'in_progress', # 默认状态为进行中
          is_effect: true
        )
        flow_version = organization_flow.flow_versions.find_by(status: true)
        raise '未找到启用的流程版本' if flow_version.blank?

        flow_version.flow_steps.order(:order_number).each do |flow_step|
          approval_step = approval_flow.approval_steps.create!(
            order_number: flow_step.order_number,
            name: flow_step.name,
            review_type: flow_step.review_type,
            organization_id: flow_step.organization_id,
            status: 'waiting' # 初始状态为等待中
          )

          ids_array = flow_step.review_user_ids.split(',').map(&:to_i)
          ids_array.each_with_index do |user_id, i|
            approval_step.approval_step_users.create!(
              order_number: i+1,
              status: 'waiting', # 初始状态为等待中
              organization_id: flow_step.organization_id,
              user_id: user_id
            )
          end
        end
        approval_flow.start_flow!
        approval_flow
      end
    rescue => exception
      Rails.logger.error("创建审批流程异常: #{exception}")
      raise exception
    end
  end

  def start_flow!
    # 如果没有审核步骤，直接通过审核
    if self.approval_steps.blank?
      self.update!(status: 'approved')
    else
      approval_step = self.approval_steps.order(:order_number).first
      self.update!(current_step_id: approval_step.id)
      approval_step.update!(status: 'in_progress')
      if approval_step.counter_sign? || approval_step.or_sign? # 会签 || 或签
        approval_step.approval_step_users.update_all(status: 'in_progress')
      else
        approval_step.approval_step_users.order(:order_number).first.update!(status: 'in_progress')
      end
    end
  end

  def source_type
    case flowable.class.to_s
    when 'ProjectOrganization'
      '项目'
    when 'WorkOrder'
      '工单'
    when 'Organization'
      '组织'
    else
      '未知'
    end
  end

  def source_name
    case flowable.class.to_s
    when 'ProjectOrganization'
      flowable.name
    when 'Organization'
      flowable.name
    else
      '未知'
    end
  end

  def source_title_id
    case flowable.class.to_s
    when 'ProjectOrganization'
      [flowable.project_id, flowable.project.name]
    else
      [flowable_id, ""]
    end
  end

  private
  def change_status
    return unless self.previous_changes.has_key?(:status)
    change_commend = self.previous_changes
    if change_commend[:status].last == 'approved' || change_commend[:status].last == 'rejected'
      self.flowable.approved_status
      title = "审批流程状态变动提醒【#{self.source_type}】"
      content = "你发起人审批流程(#{source_name} 申请) 已变更为【#{self.status}】，请查看详情。"
      UserNotice.init_message!(organization_id, user_id, flowable, title, content, url = nil)
    end
  end
end
