# == Schema Information
#
# Table name: role_permissions
#
#  id                           :bigint           not null, primary key
#  deleted_at(删除时间)         :datetime
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  organization_id(组织ID)      :integer
#  permission_action_id(权限ID) :integer
#  role_id(角色ID)              :integer
#
# Indexes
#
#  index_role_permissions_on_deleted_at            (deleted_at)
#  index_role_permissions_on_organization_id       (organization_id)
#  index_role_permissions_on_permission_action_id  (permission_action_id)
#  index_role_permissions_on_role_id               (role_id)
#
class RolePermission < ApplicationRecord
  acts_as_paranoid

  belongs_to :role
  belongs_to :permission_action
end
