class Oauth2Service
  include HTTParty

  def initialize
    @config = Settings.oauth2
    @base_url = @config.base_url
    @client_id = @config.client_id
    @client_secret = @config.client_secret
    @redirect_uri = @config.redirect_uri
  end

  def authorization_url
    params = {
      response_type: 'code',
      client_id: @client_id,
      redirect_uri: @redirect_uri
    }

    "#{@base_url}#{@config.authorize_path}?#{params.to_query}"
  end

  def get_access_token(auth_code)
    url = "#{@base_url}#{@config.token_path}"
    params = {
      code: auth_code,
      grant_type: 'authorization_code',
      client_id: @client_id,
      client_secret: @client_secret
    }

    begin
      response = HTTParty.get(url, query: params)
      if response.success?
        data = response.parsed_response
        if data['code'] == 200
          {
            success: true,
            data: data['data']
          }
        else
          {
            success: false,
            error: data['message'] || '获取访问令牌失败'
          }
        end
      else
        {
          success: false,
          error: "HTTP错误: #{response.code}"
        }
      end
    rescue => e
      {
        success: false,
        error: "网络错误: #{e.message}"
      }
    end
  end

  def get_user_info(access_token)
    url = "#{@base_url}#{@config.userinfo_path}"
    headers = {
      'Authorization' => access_token
    }

    begin
      response = HTTParty.get(url, headers: headers)
      if response.success?
        data = response.parsed_response
        if data['code'] == 200
          {
            success: true,
            data: data['data']
          }
        else
          {
            success: false,
            error: data['msg'] || '获取用户信息失败'
          }
        end
      else
        {
          success: false,
          error: "HTTP错误: #{response.code}"
        }
      end
    rescue => e
      {
        success: false,
        error: "网络错误: #{e.message}"
      }
    end
  end

  def logout(user_id, access_token)
    url = "#{@base_url}#{@config.logout_path}"
    params = {
      userId: user_id
    }

    headers = {
      'Authorization' => access_token
    }

    begin
      response = HTTParty.get(url, query: params, headers: headers)

      if response.success?
        {
          success: true
        }
      else
        {
          success: false,
          error: "HTTP错误: #{response.code}"
        }
      end
    rescue => e
      {
        success: false,
        error: "网络错误: #{e.message}"
      }
    end
  end
end
