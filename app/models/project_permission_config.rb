# == Schema Information
#
# Table name: project_permission_configs
#
#  id                            :bigint           not null, primary key
#  deleted_at(删除时间)          :datetime
#  key(权限key)                  :string(200)
#  name(权限名称)                :string
#  order_number(排序)            :integer
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#  organization_id(外键: 组织id) :integer
#  parent_id(父级id)             :integer
#
# Indexes
#
#  index_project_permission_configs_on_organization_id  (organization_id)
#

class ProjectPermissionConfig < ApplicationRecord

  acts_as_paranoid

  # belongs_to :organization
  has_many :user_role_permissions, dependent: :destroy
  belongs_to :parent_project_permission_config, foreign_key: :parent_id, class_name: :ProjectPermissionConfig, optional: true
  has_many :child_project_permission_configs, foreign_key: :parent_id, class_name: :ProjectPermissionConfig, dependent: :destroy

  def self.generate_permission(organization_id)
    permissions = [
      {order_number: 1, name: '信息查看类', children: [
        { order_number: 2, key: 'view_projects', name: '查看项目信息' },
        { order_number: 3, key: 'view_plans', name: '查看计划信息' },
        { order_number: 4, key: 'view_problem', name: '查看缺陷信息' },
        { order_number: 5, key: 'view_demand', name: '查看需求信息' },
        { order_number: 5, key: 'set_member', name: '配置成员' },
        { order_number: 5, key: 'set_organization', name: '配置企业' },
      ]},
      { order_number: 6, name: '计划管理类', children: [
        { order_number: 7, key: 'add_plan', name: '新建、导入计划' },
        { order_number: 7, key: 'edit_plan', name: '编辑' },
        { order_number: 7, key: 'destroy_plan', name: '删除' },
        { order_number: 10, key: 'show_plan', name: '计划详情' },
        { order_number: 10, key: 'export_plan', name: '导出计划' },
        { order_number: 11, key: 'milestone_change', name: '里程碑变更' }
      ]},
      { order_number: 12, name: '工单管理类', children: [
        { order_number: 13, key: 'new_work_orders', name: '新建工单信息' },
        { order_number: 14, key: 'view_work_orders', name: '查看工单信息' },
        { order_number: 15, key: 'FAE', name: 'FAE' },
        { order_number: 16, key: 'export_work_order', name: '导出工单' },
        { order_number: 17, key: 'import_work_order', name: '导入工单' },
      ]},
      { order_number: 18, name: '风险管理类', children: [
        { order_number: 19, key: 'new_project_risks', name: '新建风险信息' },
        { order_number: 20, key: 'view_project_risks', name: '查看风险信息' },
        { order_number: 21, key: 'export_project_risk', name: '导出项目风险' },
        { order_number: 22, key: 'import_project_risk', name: '导入项目风险' },
      ]},
    ]
    # 插入数据
    begin
      ActiveRecord::Base.transaction do
        i = 1
        permissions.each do |permission|
          # 插入主级别
          first_config = ProjectPermissionConfig.create_with(order_number: i).find_or_create_by!(name: permission[:name], organization_id: organization_id)
          i += 1
          # 插入子级别
          permission[:children].each do |children_hash|
            ProjectPermissionConfig.create_with(order_number: i, name: children_hash[:name]).find_or_create_by!(parent_id: first_config.id, key: children_hash[:key], organization_id: organization_id)
            i += 1
          end
        end
      end
    rescue => exception
      p exception.message
    end

  end
end
