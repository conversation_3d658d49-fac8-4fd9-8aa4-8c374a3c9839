# == Schema Information
#
# Table name: group_users
#
#  id                      :bigint           not null, primary key
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  group_id(用户组ID)      :integer
#  organization_id(组织ID) :integer
#  user_id(用户ID)         :integer
#
# Indexes
#
#  index_group_users_on_group_and_user   (group_id,user_id) UNIQUE
#  index_group_users_on_group_id         (group_id)
#  index_group_users_on_organization_id  (organization_id)
#  index_group_users_on_user_id          (user_id)
#
class GroupUser < ApplicationRecord
  belongs_to :group
  belongs_to :user
  belongs_to :organization

  validates :user_id, uniqueness: { scope: :group_id, message: '用户不能重复添加到同一个用户组' }

  # 验证用户和组织的一致性
  validate :user_organization_consistency

  private

  def user_organization_consistency
    if user && group && user.organization_id != group.organization_id
      errors.add(:user, '用户必须属于同一个组织')
    end
  end
end
