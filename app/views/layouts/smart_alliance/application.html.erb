<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <title>项目管理系统</title>
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

  <%= stylesheet_link_tag 'admin/application', media: 'all' %>
  <%= javascript_include_tag 'admin/application', media: 'all' %>
</head>

<body class="layui-layout-body" id="LAY_home_iframe">
  <div id="LAY_app" style="visibility: hidden">
    <div class="layui-layout layui-layout-admin">
      <%= render "layouts/admin/header"%>
      <!-- <div class="layui-header">
        <div class="layui-logo layui-hide-xs layui-bg-black">AI服务系统</div>
        <ul class="layui-nav layui-layout-left" style="line-height: 60px;">
          <% if request.fullpath != '/admin' %>
            <% actions = request.fullpath.split('/')  %>
            <% actions.delete('') %>
            <%= crumbs_list(actions) %>
          <% end %>
        </ul>
      </div> -->
      <%= render "layouts/admin/left_nav"%>
      <%= render "layouts/admin/pagetabs"%>

      <!-- 主体内容 -->
      <div class="layui-body" id="LAY_app_body">
        <div class="layadmin-tabsbody-item layui-show">
          <iframe src="<%= admin_welcomes_path %>" frameborder="0" class="layadmin-iframe"></iframe>
        </div>
      </div>

      <div class="layadmin-body-shade" layadmin-event="shade"></div>
    </div>
    <%#= render "layouts/admin/notice" %>
    <%#= yield :script %>

  </div>
  <script>
    layui.config({
        base: '/assets/smart_alliance/' // 静态资源所在路径
    }).use(['index']);

    $(document).on('click', '#change-password', function(){
      layer.open({
        type: 1,
        area: '350px',
        resize: false,
        shade: 0.8,
        shadeClose: false,
        title: "修改密码",
        content: `
          <div class="layui-form" lay-filter="filter-test-layer" style="margin: 16px;">
            <div class="demo-login-container">
              <div class="layui-form-item">
                <div class="layui-input-wrap">
                  <div class="layui-input-prefix" style='right: auto;'>
                    <i class="layui-icon layui-icon-password"></i>
                  </div>
                  <input type="password" name="password" value="" lay-verify="required" placeholder="旧密码" lay-reqtext="旧密码" autocomplete="off" class="layui-input" lay-affix="eye">
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-wrap">
                  <div class="layui-input-prefix" style='right: auto;'>
                    <i class="layui-icon layui-icon-password"></i>
                  </div>
                  <input type="password" name="new_password" value="" lay-verify="required" placeholder="新密码" lay-reqtext="请填写新密码" autocomplete="off" class="layui-input" lay-affix="eye">
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-wrap">
                  <div class="layui-input-prefix" style='right: auto;'>
                    <i class="layui-icon layui-icon-password"></i>
                  </div>
                  <input type="password" name="new_password_confirmation" value="" lay-verify="required" placeholder="确认密码" lay-reqtext="请再次填写密码" autocomplete="off" class="layui-input" lay-affix="eye">
                </div>
              </div>

              <div class="layui-form-item">
                <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="file_user_set_update_password">保存</button>
              </div>

            </div>
          </div>
        `,
        success: function(){
          // 对弹层中的表单进行初始化渲染
          layui.form.render();
          // 表单提交事件
          layui.form.on('submit(file_user_set_update_password)', function(data){
            var field = data.field; // 获取表单字段值
            $.ajax({
              type: "POST",
              url: '/admin/sessions/update_password',
              data: field,
              success:function(res){
                if (res.status){
                  window.location.href = '/admin/sessions'
                }else{
                  layer.msg(res.msg)
                }
              }
            })
            return false; // 阻止默认 form 跳转
          });
        }
      });
    })

    // 等待 Layui 和 DOM 加载完成
    layui.use(['form'], function () {
      const $ = layui.jquery;
      const form = layui.form;

      const SWITCH_KEY = 'refreshShortcutEnabled';
      const IFRAME_ID = 'layadmin-iframe'; // 你的 iframe ID
      const TRUSTED_ORIGIN = 'https://your-admin-domain.com'; // ⚠️ 替换为你的实际域名，生产环境务必设置！'*' 有安全风险

      // --- 1. 初始化开关状态 ---
      function initSwitchState() {
        // 读取缓存，如果不存在，默认为 'true'（开启）
        let savedState = localStorage.getItem(SWITCH_KEY);
        let enableOnLoad = savedState === null ? false : savedState === 'true'; // 注意：null 时默认 true

        // 设置开关状态
        $(`input[name="open_refresh_status"]`).prop('checked', enableOnLoad);
        form.render(); // 重新渲染 switch 样式

        // 将当前状态同步给 iframe (包括首次加载和状态改变)
        syncStateToIframe(enableOnLoad);

        return enableOnLoad;
      }

      // --- 2. 同步状态到 iframe ---
      function syncStateToIframe(enabled) {
        const iframe = document.getElementById(IFRAME_ID);
        if (iframe && iframe.contentWindow) {
          try {
            iframe.contentWindow.postMessage({
              type: 'REFRESH_SHORTCUT_SYNC',
              enabled: enabled
            }, TRUSTED_ORIGIN); // ⚠️ 使用具体 origin 更安全
            console.log('已同步刷新快捷键状态到 iframe:', enabled);
          } catch (error) {
            console.warn('发送消息到 iframe 失败 (可能跨域或未加载):', error);
          }
        }
      }

      // --- 3. 监听开关变化 ---
      form.on('switch(switchTest)', function (obj) {
        const checked = obj.elem.checked;
        localStorage.setItem(SWITCH_KEY, checked);
        // 状态改变时，立即同步到 iframe
        syncStateToIframe(checked);
      });

      // --- 4. 监听来自 iframe 的刷新请求 ---
      window.addEventListener('message', function (event) {
        // ✅ 安全检查：验证消息来源 (强烈建议)
        // if (event.origin !== TRUSTED_ORIGIN) {
        //   console.warn('忽略来自非可信源的消息:', event.origin);
        //   return;
        // }

        // 检查消息类型和数据结构
        if (event.data && event.data.type === 'REFRESH_SHORTCUT_REQUEST') {
          console.log('收到 iframe 的刷新请求');
          // 执行刷新操作
          const refreshBtn = $('a[layadmin-event="refresh"]');
          if (refreshBtn.length > 0) {
            refreshBtn.click();
            console.log('已触发刷新按钮点击');
          } else {
            console.warn('未找到刷新按钮 [layadmin-event="refresh"]');
          }
        }
      });

      // --- 5. (可选) 监听父页面自身的键盘事件 ---
      // 如果用户在父页面 UI (如侧边栏) 上操作时也想用快捷键，可以保留
      $(document).on('keydown', function (e) {
        const isSwitchOn = localStorage.getItem(SWITCH_KEY) === 'true';
        if (!isSwitchOn) return;

        const isRefreshKey = (
          e.key === 'F5' ||
          (e.ctrlKey && e.key.toLowerCase() === 'r') ||
          (e.metaKey && e.key.toLowerCase() === 'r') // Mac Cmd+R
        );

        if (isRefreshKey) {
          e.preventDefault();
          e.stopPropagation(); // 防止事件冒泡到 iframe 或其他地方
          $('a[layadmin-event="refresh"]').click();
        }
      });

      // --- 6. 监听 iframe 加载事件，确保状态同步 ---
      $(`#${IFRAME_ID}`).on('load', function () {
        console.log('iframe 加载完成，同步状态');
        // iframe 加载完成后，立即同步当前开关状态
        const currentState = localStorage.getItem(SWITCH_KEY) === 'true';
        syncStateToIframe(currentState);
      });

      // --- 7. 初始化 ---
      initSwitchState();

      // --- 调试辅助 ---
      // window.refreshShortcutDebug = { SWITCH_KEY, IFRAME_ID, TRUSTED_ORIGIN };
    });
  </script>

</body>
</html>

