<!DOCTYPE html>
<html>
<head>
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

  <%= stylesheet_link_tag 'admin/application', media: 'all' %>
  <%= stylesheet_link_tag '/ckeditor5/ckeditor5.css', media: 'all' %>
  <%= javascript_include_tag 'admin/application', media: 'all' %>
  <%#= javascript_include_tag 'smart_alliance/index' %>
</head>

<body layadmin-themealias="default">
  <div class="layui-fluid">
    <%= yield %>
  </div>
</body>
<%= render "layouts/admin/notice" %>

<script>
  layui.config({
      base: '/assets/smart_alliance/' // 静态资源所在路径
  }).use(['index']);

  (function() {
    // --- 1. 存储从父页面接收的状态 ---
    let parentRefreshEnabled = true; // 默认开启，等待父页面同步

    // --- 2. 监听来自父页面的状态同步消息 ---
    window.addEventListener('message', function(event) {
      // ✅ 安全检查 (可选，如果父页面设置了 TRUSTED_ORIGIN)
      // if (event.origin !== 'https://parent-domain.com') return;

      if (event.data && event.data.type === 'REFRESH_SHORTCUT_SYNC') {
        parentRefreshEnabled = event.data.enabled;
        console.log('iframe 收到父页面状态同步:', parentRefreshEnabled);
        // 也可以选择存到 localStorage 以便持久化
        // localStorage.setItem('refreshShortcutEnabled', parentRefreshEnabled);
      }
    });

    // --- 3. 监听键盘事件 (核心) ---
    document.addEventListener('keydown', function(e) {
      // 1. 检查是否是刷新快捷键
      const isRefreshKey = (
        e.key === 'F5' ||
        (e.ctrlKey && e.key.toLowerCase() === 'r') ||
        (e.metaKey && e.key.toLowerCase() === 'r') // Cmd+R on Mac
      );

      if (!isRefreshKey) return;

      // 2. 检查父页面开关状态 (以父页面为准)
      if (!parentRefreshEnabled) {
        console.log('刷新快捷键已禁用 (由父页面控制)');
        return; // 不做任何处理，允许默认行为（页面内刷新）
      }

      // 3. 阻止 iframe 内部的默认刷新行为
      e.preventDefault();
      e.stopPropagation(); // 阻止事件冒泡

      // 4. 发送消息给父页面
      try {
        window.parent.postMessage({
          type: 'REFRESH_SHORTCUT_REQUEST',
          timestamp: Date.now(), // 可选：用于调试去重
          key: e.key,
          ctrlKey: e.ctrlKey,
          metaKey: e.metaKey
        }, '*'); // ⚠️ 生产环境建议替换 '*' 为父页面确切的 origin
        console.log('已发送刷新请求至父页面');
      } catch (error) {
        console.error('向父页面发送消息失败:', error);
      }
    });

    // --- 4. 页面加载完成后，可以主动请求一次状态 (可选) ---
    // window.addEventListener('load', function() {
    //   window.parent.postMessage({ type: 'REFRESH_SHORTCUT_REQUEST_STATE' }, '*');
    // });

  })();(function() {
    // --- 1. 存储从父页面接收的状态 ---
    let parentRefreshEnabled = true; // 默认开启，等待父页面同步

    // --- 2. 监听来自父页面的状态同步消息 ---
    window.addEventListener('message', function(event) {
      // ✅ 安全检查 (可选，如果父页面设置了 TRUSTED_ORIGIN)
      // if (event.origin !== 'https://parent-domain.com') return;

      if (event.data && event.data.type === 'REFRESH_SHORTCUT_SYNC') {
        parentRefreshEnabled = event.data.enabled;
        console.log('iframe 收到父页面状态同步:', parentRefreshEnabled);
        // 也可以选择存到 localStorage 以便持久化
        // localStorage.setItem('refreshShortcutEnabled', parentRefreshEnabled);
      }
    });

    // --- 3. 监听键盘事件 (核心) ---
    document.addEventListener('keydown', function(e) {
      // 1. 检查是否是刷新快捷键
      const isRefreshKey = (
        e.key === 'F5' ||
        (e.ctrlKey && e.key.toLowerCase() === 'r') ||
        (e.metaKey && e.key.toLowerCase() === 'r') // Cmd+R on Mac
      );

      if (!isRefreshKey) return;

      // 2. 检查父页面开关状态 (以父页面为准)
      if (!parentRefreshEnabled) {
        console.log('刷新快捷键已禁用 (由父页面控制)');
        return; // 不做任何处理，允许默认行为（页面内刷新）
      }

      // 3. 阻止 iframe 内部的默认刷新行为
      e.preventDefault();
      e.stopPropagation(); // 阻止事件冒泡

      // 4. 发送消息给父页面
      try {
        window.parent.postMessage({
          type: 'REFRESH_SHORTCUT_REQUEST',
          timestamp: Date.now(), // 可选：用于调试去重
          key: e.key,
          ctrlKey: e.ctrlKey,
          metaKey: e.metaKey
        }, '*'); // ⚠️ 生产环境建议替换 '*' 为父页面确切的 origin
        console.log('已发送刷新请求至父页面');
      } catch (error) {
        console.error('向父页面发送消息失败:', error);
      }
    });

    // --- 4. 页面加载完成后，可以主动请求一次状态 (可选) ---
    // window.addEventListener('load', function() {
    //   window.parent.postMessage({ type: 'REFRESH_SHORTCUT_REQUEST_STATE' }, '*');
    // });

  })();

  // 更新用户通知数量
  updateUserNotice('<%= current_user.user_notices.where(status: false).count %>');
</script>

</html>