<div class="layui-card">
  <div class="layui-card-body">

    <div class="layui-form flex-item-around">
      <h4>你正在编辑“<%= @flow_version.organization_flow.name %>”的审核流程(<span style="color: #ff5722;">更新或新增流程对已发起的流程不造成影响</span>)</h4>

      <div class="layui-form flex" style="justify-content: end; align-items: center;">
        <div style="width: 100px;">
          <select lay-filter="current_flow_version_id">
            <% @flow_version.organization_flow.flow_versions.each do |flow_version| %>
              <option value="<%= flow_version.id %>" <%= flow_version.id == @flow_version.id ? 'selected' : ''%>>V<%= flow_version.version %></option>
            <% end %>
          </select>
        </div>
        <% if @flow_version.status %>
          <button type="button" class="layui-btn layui-btn-normal layui-btn-radius jh-btn-normal layui-btn-sm m-l-10 new_version">新建版本</button>
          <button type="button" class="layui-btn layui-btn-normal layui-btn-radius jh-btn-normal layui-btn-sm m-l-10 layui-btn-disabled">保存</button>
          <button type="button" class="layui-btn layui-btn-normal layui-btn-radius jh-btn-danger layui-btn-sm m-l-10 layui-btn-disabled">发布版本</button>
        <% else %>
          <button type="button" class="layui-btn layui-btn-normal layui-btn-radius jh-btn-normal layui-btn-sm m-l-10 new_version">新建版本</button>
          <button type="button" class="layui-btn layui-btn-normal layui-btn-radius jh-btn-normal layui-btn-sm m-l-10 save_button">保存</button>
          <button type="button" class="layui-btn layui-btn-normal layui-btn-radius jh-btn-danger layui-btn-sm m-l-10 fb_button">发布版本</button>
        <% end %>
      </div>
    </div>
    <hr>

    <%= simple_nested_form_for([:admin, @flow_version], remote: true, html: {class: 'layui-form'}, wrapper: :seven_form_line) do |f| %>
      <%= f.error_notification %>
      <% if @flow_version.status %>
        <blockquote class="layui-elem-quote">
          已启用中的流程不可编辑，如需编辑请创建新流程
        </blockquote>
      <% end %>
      <%= f.input :status, label: '发布状态', input_html: {checked: f.object.status}, wrapper_html: {style: 'display:none'} %>
      <%= f.fields_for :flow_steps, @flow_version.flow_steps.order(:order_number), wrapper: :seven_form_line do |flow_step| %>
        <div class='flex flow_steps_form'>
          <%= flow_step.input :order_number, label: '序号', wrapper_html: {style: 'width: 200px;'}, input_html: {'lay-verify': "required", style: 'width: 42px; padding-right: 0px; padding-left: 0px; text-align: center;'} %>
          <%= flow_step.input :name, label: '步骤名称', input_html: {'lay-verify': "required"} %>
          <%= flow_step.input :review_type, label: '审核类型', input_html: {'lay-verify': "required"} %>
          <%= flow_step.input :copy_review_user_ids, label: '审核人员', wrapper_html: {style: 'display:none'} %>

          <div class="layui-form string optional">
            <div class="layui-form-item">
              <label class="layui-form-label string optional" for="">审核人员</label>
              <div class="layui-input-block flex-item">
                <div style="width: 250px;" class="select_review_user_ids" data-value="<%= flow_step.object.xm_data_users.to_json %>"></div>
              </div>
            </div>
          </div>

          <%= flow_step.input :flow_version_id, label: '版本', input_html: {'lay-verify': "required", value: @flow_version.id}, wrapper_html: {style: 'display:none'} %>
          <%= flow_step.input :organization_id, label: '组织ID', input_html: {'lay-verify': "required", value: current_organization_id}, wrapper_html: {style: 'display:none'} %>


          <% unless @flow_version.status %>
            <%= flow_step.link_to_remove class: 'file_down destroy_flow_step', data:{confirm: '确认删除吗？'}, style: 'color: #FF6941; line-height: 2.3; width: 55px; margin-left: 10px;' do %>
              <i class="layui-icon layui-icon-delete" style="font-size: 17px;"></i>删除
            <% end %>
          <% end %>
        </div>
      <% end %>
      <% unless @flow_version.status %>
        <%= f.link_to_add :flow_steps, class: 'add_flow_steps file_down', style: 'color: #0074C8 !important; margin-left: 80px; margin-bottom: -3px;' do %> <i class="layui-icon" style="font-weight: 600;">&#xe608;</i> 新增步骤 <% end %>
      <% end %>

      <%#= f.input :version %>
      <%#= f.input :status %>
      <%#= f.input :organization_flow_id %>
      <%#= f.input :organization_id %>
      <%#= f.input :user_id %>

      <div class="actions" style='display: none'>
        <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
        <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
      </div>
    <% end %>

  </div>
</div>

<script>
  var layer, form;
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form', 'xmSelect'], function () {
      layer = layui.layer,
      form = layui.form;
      // 切换版本
      form.on('select(current_flow_version_id)', function(data){
        var value = data.value; // 获得被选中的值
        window.location.href = `/admin/flow_versions/${data.value}/edit`;
      });

      form.render();
    });
  });

  function sortNumber(){
    $('.flow_steps_form').parents('.fields:visible').find('.flow_version_flow_steps_order_number input').each(function(i){
      $(this).val(i+1);
    });
  }

  $(document).on('click', '.destroy_flow_step', function(){
    sortNumber();
    form.render();
  });

  $(document).on('click', '.add_flow_steps', function(){
    sortNumber();
    form.render();

    var selectRoleElement = $('.flow_steps_form').last().parent('.fields:visible').find('.select_review_user_ids');
    var data = JSON.parse(selectRoleElement.attr('data-value'));
    var name = $('.flow_steps_form').last().parent('.fields:visible').find('.flow_version_flow_steps_copy_review_user_ids input').attr('name')
    name = name.replace('copy_review_user_ids', 'review_user_ids')

    layui.xmSelect.render({
      el: selectRoleElement[0], // 将jQuery对象转换为DOM元素
      autoRow: true,
      name: name,
      toolbar: { show: true },
      tips: '选择人员, 将按照选择顺序进行审核',
      filterable: true,
      paging: true,
      pageRemote: true,
      layVerify: 'required',
      remoteSearch: true,
      remoteMethod: function(val, cb, show, pageIndex){
        //这里引入了一个第三方插件axios, 相当于$.ajax
        $.ajax({
          url: "/admin/public_api/search_users",
          type: 'GET',
          data: { q: val, page: pageIndex },
          success:function(res){
            cb(res.data, res.total_page)
          }
        })
      }

    });
  });

  // 提交表单
  $(document).on('click', '.save_button', function(){
    $('#flow_version_status').prop('checked', false);
    $('.save_btn').click();
  })

  // 发布版本
  $(document).on('click', '.fb_button', function(){
    $('#flow_version_status').prop('checked', true);
    $('.save_btn').click();
  })

  // 新建版本
  $(document).on('click', '.new_version', function(){
    layer.load(1);
    $.ajax({
      url: "/admin/flow_versions",
      type: 'POST',
      data: {
        organization_flow_id: `<%= @flow_version.organization_flow_id %>`,
        flow_version_id: `<%= @flow_version.id %>`
      }
    })
  })

  $('.flow_steps_form').each(function(){
    var selectRoleElement = $(this).parent('.fields:visible').find('.select_review_user_ids');
    var data = JSON.parse(selectRoleElement.attr('data-value'));
    var name = $(this).parent('.fields:visible').find('.flow_version_flow_steps_copy_review_user_ids input').attr('name')
    name = name.replace('copy_review_user_ids', 'review_user_ids')

    layui.xmSelect.render({
      el: selectRoleElement[0], // 将jQuery对象转换为DOM元素
      autoRow: true,
      name: name,
      toolbar: { show: true },
      tips: '选择人员, 将按照选择顺序进行审核',
      filterable: true,
      paging: true,
      pageRemote: true,
      layVerify: 'required',
      remoteSearch: true,
      remoteMethod: function(val, cb, show, pageIndex){
        //这里引入了一个第三方插件axios, 相当于$.ajax
        if(!val){
          return cb(data);
        }
        $.ajax({
          url: "/admin/public_api/search_users",
          type: 'GET',
          data: { q: val, page: pageIndex },
          success:function(res){
            cb(res.data, res.total_page)
          }
        })
      }

    });
  })

</script>