<%= simple_form_for([:admin, @group], remote: true, wrapper: :seven_form_line, html: {id: 'group_form', class: 'layui-form', style: 'margin: 20px;', novalidate:'novalidate' }) do |f| %>
  <%= f.input :name, label: '用户组名称', input_html: { 'lay-verify': "required" } %>
  <%= f.input :description, label: '描述', as: :text, input_html: {class: 'layui-textarea'} %>

  <div class="layui-form string optional">
    <div class="layui-form-item">
      <label class="layui-form-label string optional" for="">组成员</label>
      <div class="layui-input-block flex-item">
        <div id="select_users" style="width: 99%;" data-value="[]" data-selected="<%= @group.persisted? ? @group.users.pluck(:id).to_json : '[]' %>"></div>
      </div>
    </div>
  </div>

  <div class="actions" style="display: none;">
    <%= f.submit '保存', data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': true %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  var layer, form; //保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
      layer = layui.layer,
      form = layui.form;
      form.render();
    });
  });

  var select_users;
  layui.use(['xmSelect'], function () {
    var xmSelect = layui.xmSelect

    // 获取可用用户列表
    // 先获取已选中的用户ID
    var selectedUserIds = JSON.parse($('#select_users').attr('data-selected'));

    // 远程搜索+默认选中
    select_users = xmSelect.render({
      el: '#select_users',
      autoRow: true,
      toolbar: { show: true },
      tips: '请选择组成员',
      filterable: true,
      paging: true,
      pageRemote: true,
      searchTips: '请输入用户名',
      remoteSearch: true,
      layVerify: 'required',
      initValue: selectedUserIds, // 默认选中
      remoteMethod: function(val, cb, show, pageIndex){
        // 如果val为空, 不触发搜索
        // if(!val){
        //   return cb([]);
        // }
        $.ajax({
          url: "/admin/public_api/search_users",
          type: 'GET',
          data: {
            q: val,
            page: pageIndex,
            group_id: '<%= @group.persisted? ? @group.id : "" %>'
          },
          success:function(res){
            cb(res.data, res.total_page)
          }
        })
      },
      // 初始加载时，拉取一次数据用于展示已选中
      data: [],
      template: function(item, sels, name, value){
        var displayName = (item && item.name) ? item.name : '';
        var displayPhone = (item && item.phone) ? ' (' + item.phone + ')' : '';
        if (!displayName && !displayPhone) {
          return '';
        }
        return displayName + displayPhone;
      }
    });

    // 如果有已选中的用户，初始拉取一次数据用于展示
    if(selectedUserIds.length > 0){
      $.ajax({
        url: "/admin/public_api/search_users",
        type: 'GET',
        data: {
          group_id: '<%= @group.persisted? ? @group.id : "" %>',
          q: '' // 拉全部
        },
        success: function(res){
          // 只保留已选中的用户
          var selectedData = res.data.filter(function(item){
            return selectedUserIds.indexOf(item.value) !== -1;
          });
          select_users.update({ data: selectedData });
        }
      });
    }
  })
</script>
