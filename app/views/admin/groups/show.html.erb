<div class="layui-card">
  <div class="layui-card-header">
    <h3><%= @group.name %> - 组成员管理</h3>
    <% if @group.description.present? %>
      <p style="color: #666; margin-top: 5px;"><%= @group.description %></p>
    <% end %>
  </div>
  <div class="layui-card-body">
    <table id="table_group_users" lay-filter="group-users-table"></table>
  </div>
</div>

<script type="text/html" id="userBarDemo">
  <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="remove" title="移除"> <i class="layui-icon layui-icon-delete"></i></a>
</script>
<script type="text/html" id="userMyBar">
  <button lay-event="add_user" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-add-1"></i>添加成员</button>
</script>

<script>
  var table;
  layui.use(function(){
  table = layui.table
  var layer = layui.layer //弹层
    ,laypage = layui.laypage //分页
    ,page = 1//页码全局变量
    ,limit = 10;//分页大小全局变量

    table.render({
      id: 'groupUsersPage',
      elem: '#table_group_users',
      url: `/admin/groups/<%= @group.id %>`, //数据接口
      title: '组成员列表',
      page: true, //开启分页
      skin: 'line',
      limit: limit,
      toolbar: '#userMyBar',
      cols: [[
        {field: 'id', align:'left', title: 'ID', hide: true},
        {field: 'avatar', align:'left', title: '头像', width: 80, templet: function(d) {
          return `<img src='${d.avatar}' style='width: 25px; height: 25px; border-radius: 50%;'>`
        }},
        {field: 'name', align:'left', title: '姓名', minWidth: 120},
        {field: 'phone', align:'left', title: '手机号', minWidth: 130},
        {field: 'created_at', align:'center', title: '加入时间', width: 180},
        {fixed: 'right', title: '操作', width: 100, align: 'center', toolbar: '#userBarDemo'}
      ]]
    });

    //监听行工具事件
	  table.on('tool(group-users-table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
      var data = obj.data //获得当前行数据
      ,layEvent = obj.event; //获得 lay-event 对应的值
      if (layEvent === 'remove'){
        layer.confirm('确认将该用户移出用户组吗？', function(index){
          layer.close(index);
          $.ajax({
            type: 'DELETE',
            url: `/admin/groups/<%= @group.id %>/remove_user`,
            data: {
              user_id: data.id
            },
            success: function(response) {
              if (response.status) {
                layer.msg(response.msg, {icon: 1});
                table.reload('groupUsersPage');
              } else {
                layer.msg(response.msg, {icon: 2});
              }
            }
          })
        })
      }
	  });

    //监听头工具栏事件
    table.on('toolbar(group-users-table)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id)
      ,data = checkStatus.data; //获取选中的数据
      switch(obj.event){
        case 'add_user':
          // 添加用户到组
          layer.open({
            type: 1,
            title: '添加组成员',
            area: ['400px', '300px'],
            content: `
              <div style="padding: 20px;">
                <div class="layui-form-item">
                  <label class="layui-form-label">选择用户</label>
                  <div class="layui-input-block">
                    <div id="select_add_user" style="width: 100%;"></div>
                  </div>
                </div>
              </div>
            `,
            btn: ['添加', '取消'],
            yes: function(index, layero) {
              var selectedUsers = select_add_user.getValue();
              if (selectedUsers.length === 0) {
                layer.msg('请选择要添加的用户', {icon: 2});
                return;
              }

              // 添加用户到组
              $.ajax({
                type: 'POST',
                url: `/admin/groups/<%= @group.id %>/add_user`,
                data: {
                  user_id: selectedUsers[0].value
                },
                success: function(response) {
                  if (response.status) {
                    layer.msg(response.msg, {icon: 1});
                    layer.close(index);
                    table.reload('groupUsersPage');
                  } else {
                    layer.msg(response.msg, {icon: 2});
                  }
                }
              });
            },
            success: function(layero, index) {
              // 初始化用户选择器，增加远程搜索
              layui.use(['xmSelect'], function () {
                var xmSelect = layui.xmSelect;
                select_add_user = xmSelect.render({
                  el: '#select_add_user',
                  radio: true,
                  tips: '请选择用户',
                  filterable: true,
                  searchTips: '请输入用户名或手机号搜索',
                  remoteSearch: true,
                  remoteMethod: function(val, cb, show){
                    // 如果val为空, 不触发搜索
                    if(!val){
                      return cb([]);
                    }
                    $.ajax({
                      url: '/admin/groups/available_users',
                      type: 'GET',
                      data: {
                        group_id: '<%= @group.id %>',
                        q: val
                      },
                      success: function(response) {
                        if (response.status) {
                          cb(response.data);
                        } else {
                          cb([]);
                        }
                      }
                    });
                  },
                  data: [],
                  template: function(item, sels, name, value){
                    var displayName = (item && item.name) ? item.name : '';
                    var displayPhone = (item && item.phone) ? ' (' + item.phone + ')' : '';
                    if (!displayName && !displayPhone) {
                      return '';
                    }
                    return displayName + displayPhone;
                  }
                });
              });
            }
          });
        break;
      };
    });

  })
</script>
