<div class="layui-card">
  <div class="layui-card-body">
    <form class="layui-form layui-row layui-col-space16">
      <div class="layui-col-md4">
        <div class="layui-input-wrap">
          <div class="layui-input-prefix">
            <i class="layui-icon layui-icon-username"></i>
          </div>
          <input type="text" name="name_or_phone_cont" value="" placeholder="请输入姓名或手机号" class="layui-input" lay-affix="clear">
        </div>
      </div>

      <div class="layui-btn-container layui-col-md4">
        <button class="layui-btn" lay-submit lay-filter="demo-table-search">搜索</button>
        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
      </div>
    </form>

    <table id="user_table_demo" lay-filter="user-admin-table"></table>
  </div>
</div>
<script type="text/html" id="UserbarDemo">
  <% if policy(Organization).users? %>
    <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="edit"> <i class="layui-icon layui-icon-edit"></i></a>
  <% end %>

  <% if policy(Organization).users? %>
    <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="deleted"> <i class="layui-icon layui-icon-delete"></i> </a>
  <% end %>

  <% if policy(Organization).users? %>
    {{# if(d.status == 'active'){ }}
      <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="lock">锁定</a>
    {{# } else { }}
      <a class="layui-btn layui-btn-xs layui-border-blue layui-btn-primary" lay-event="un_lock">解锁</a>
    {{# } }}
  <% end %>
</script>

<script type="text/html" id="UsermyBar">
  <% if policy(Organization).users? %>
    <button lay-event="add" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-add-1"></i>新增</button>
  <% end %>
</script>

<script>
  var table;
  layui.use(function(){
  table = layui.table
  var layer = layui.layer //弹层
    ,laypage = layui.laypage //分页
    ,page = 1//页码全局变量
    ,limit = 10;//分页大小全局变量

    var form = layui.form;
    // 搜索提交
    form.on('submit(demo-table-search)', function(data){
      var field = data.field; // 获得表单字段
      // 执行搜索重载
      table.reload('UserlistPage', {
        page: {
          curr: 1 // 重新从第 1 页开始
        },
        where: field
      });
      return false; // 阻止默认 form 跳转
    });

    table.render({
      id: 'UserlistPage',
      elem: '#user_table_demo',
      url: `/admin/organizations/<%= @organization.id %>/users`, //数据接口
      title: '列表',
      page: true, //开启分页
      skin: 'line',
      limit: limit,
      toolbar: '#UsermyBar',
      cols: [[
        {field: 'id', align:'left', title: 'ID', hide: true},
        {field: 'avatar', align:'left', title: '头像', minWidth: 100, templet: function(d) {
          return `<img src='${d.avatar}' style='width: 25px;'>`
        }},
        {field: 'name', align:'left', title: '姓名', minWidth: 100},
        {field: 'phone', align:'left', title: '手机号', minWidth: 100},

        {fixed: 'right', title: '操作', minWidth: 170, align: 'left', toolbar: '#UserbarDemo'}
      ]]
    });

    //监听行工具事件
	  table.on('tool(user-admin-table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
      var data = obj.data //获得当前行数据
      ,layEvent = obj.event; //获得 lay-event 对应的值
      if(layEvent === 'edit'){
        $.ajax({
          type: 'GET',
          url: `/admin/users/${data.id}/edit`,
          data: {
          }
        })
      }else if (layEvent === 'detail'){
        $.ajax({
          type: 'GET',
          url: `/admin/user/${data.id}`,
          data: {
          }
        })
      }else if (layEvent === 'deleted'){

        layer.confirm('确认删除吗？此操作不可恢复', function(index){
          layer.close(index);
          $.ajax({
            type: 'DELETE',
            url: `/admin/users/${data.id}`,
            data: {
            }
          })
        })
      }else if (layEvent === 'lock'){
        layer.confirm('确认锁定用户账号吗？锁定后用户无法登录', function(index){
          layer.close(index);
          $.ajax({
            type: 'PUT',
            url: `/admin/users/${data.id}`,
            data: {
              tag: 'user_freeze',
              user: {
                status: 'freeze'
              }
            }
          })
        })
      }else if (layEvent === 'un_lock'){
        layer.confirm('确认解锁用户账号吗？解锁后用户恢复登录', function(index){
          layer.close(index);
          $.ajax({
            type: 'PUT',
            url: `/admin/users/${data.id}`,
            data: {
              tag: 'user_active',
              user: {
                status: 'active'
              }
            }
          })
        })
      }

	  });

    //监听头工具栏事件
    table.on('toolbar(user-admin-table)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id)
      ,data = checkStatus.data; //获取选中的数据
      switch(obj.event){
        case 'add':
          $.ajax({
            type: 'GET',
            url: `/admin/users/new`,
            data: {
              organization_id: `<%= @organization.id %>`
            }
          })
        break;
      };
    });

  })
</script>