<div class="layui-tab layui-tab-brief">
  <div class="layui-bg-gray" style="padding: 16px;">
    <div class="layui-row layui-col-space15" style="display: flex;">
      <div class="layui-col-md6" style="width: 60%; padding-top: 0px;">
        <div class="layui-card">
          <div><span class="layui-badge layui-bg-blue">任务列表</span></div>
          <div class="layui-card-body">

            <div class="layui-tab layui-tab-brief">
              <ul class="layui-tab-title">
                <li class="layui-this" id="current_week">本周任务</li>
                <li id="next_week">下周任务</li>
              </ul>
              <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                  <!-- 本周任务 -->
                  <div class="layui-card">
                    <div class="layui-card-body">
                      <form class="layui-form layui-row layui-col-space16">
                        <div class="layui-col-md5">
                          <div class="layui-input-wrap">
                            <input type="text" name="name_cont" value="" placeholder="请输入任务名称或任务发起人" class="layui-input" lay-affix="clear">
                          </div>
                        </div>

                        <div class="layui-col-md4">
                          <button class="layui-btn jh-btn-normal" lay-submit lay-filter="search_data_modules_index">搜索</button>
                        </div>
                      </form>
                      <table id="data_modules_index" lay-filter="data_modules_index"></table>
                    </div>
                  </div>
                </div>
                <!-- 下周任务 -->
                <div class="layui-tab-item">
                  <div class="layui-card">
                    <div class="layui-card-body">
                      <form class="layui-form layui-row layui-col-space16">
                        <div class="layui-col-md5">
                          <div class="layui-input-wrap">
                            <input type="text" name="name_cont" value="" placeholder="请输入任务名称或任务发起人" class="layui-input" lay-affix="clear">
                          </div>
                        </div>

                        <div class="layui-col-md4">
                          <button class="layui-btn jh-btn-normal" lay-submit lay-filter="search_data_modules_index2">搜索</button>
                        </div>
                      </form>
                      <table id="data_modules_index2" lay-filter="data_modules_index2"></table>
                    </div>
                  </div>

                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <div class="layui-bg-gray" style="width: 40%;">
        <div class="layui-row layui-col-space15">
          <div class="layui-card">
            <div><span class="layui-badge layui-bg-blue">待办快捷项</span></div>
            <div class="layui-card-body">
              <div class="layui-bg-gray" style="padding: 16px;">
                <div class="layui-row layui-col-space15">

                  <div class="layui-col-md4">
                    <div class="layui-card min-125">
                      <a href="/admin/welcomes/approval_step_users_list?tag=open" data-remote="true" class="layui-card-body flex-item p-d-4-1">
                        <img src="/images/home-4.png?1719815331" data-tag="tag" width="45" height="45">
                        <div class="m-l-5">
                          <div class="card-title">待审核</div>
                          <div class="card-count"><%= @approval_step_user_count %></div>
                        </div>
                      </a>
                    </div>
                  </div>

                 <!--  <div class="layui-col-md4">
                    <div class="layui-card min-125">
                      <a href="/admin/welcomes/approval_step_users_list?tag=open" data-remote="true" class="layui-card-body flex-item p-d-4-1">
                        <img src="/images/home-2.png?1719815331" width="45" height="45">
                        <div class="m-l-5">
                          <div class="card-title">待分配</div>
                          <div class="card-count"><%#= @wait_assign_count %></div>
                        </div>
                      </a>
                    </div>
                  </div> -->

                  <!--<div class="layui-col-md4">
                    <div class="layui-card min-125">
                      <div class="layui-card-body flex-item p-d-4-1">
                        <img src="/images/home-3.png?1719815331" width="45" height="45">
                        <div class="m-l-5">
                          <div class="card-title">未开始</div>
                          <div class="card-count task_details" data-tag="no_start" data-details="<%= @no_start_task_hash.to_json %>"><a  href="javascript:void(0)"><%= @no_start %></a ></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="layui-col-md4">
                    <div class="layui-card min-125">
                      <div class="layui-card-body flex-item p-d-4-1">
                        <img src="/images/home-4.png?1719815331" width="45" height="45">
                        <div class="m-l-5">
                          <div class="card-title">已提审</div>
                          <div class="card-count task_details" data-tag="watting_apply" data-details="<%= @watting_apply_task.to_json %>"><a  href="javascript:void(0)"><%= @watting_apply %></a ></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="layui-col-md4">
                    <div class="layui-card min-125">
                      <div class="layui-card-body flex-item p-d-4-1">
                        <img src="/images/home-2.png?1719815331" width="45" height="45">
                        <div class="m-l-5">
                          <div class="card-title">待审核</div>
                          <div class="card-count task_details" data-tag="my_auditing" data-details="<%= @my_auditing_task.to_json %>"><a  href="javascript:void(0)"><%= @my_auditing %></a ></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="layui-col-md4">
                    <div class="layui-card min-125">
                      <div class="layui-card-body flex-item p-d-4-1">
                        <img src="/images/home-3.png?1719815331" width="45" height="45">
                        <div class="m-l-5">
                          <div class="card-title">已完成</div>
                          <div class="card-count task_details" data-tag="completed" data-details="<%= @completed_task.to_json %>"><a  href="javascript:void(0)"><%= @completed %></a ></div>
                        </div>
                      </div>
                    </div>
                  </div> -->


                </div>
              </div>
            </div>
          </div>
          <!-- 项目 -->
          <div class="layui-card">
            <div><span class="layui-badge layui-bg-blue">近期参与项目</span></div>
            <div class="layui-card-body">
              <div class="layui-bg-gray" style="padding: 16px;">
                <table id="project-table" lay-filter="project-table"></table>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>

    <div class="layui-row layui-col-space15" style="display: flex;">
      <div class="layui-col-md6" style="width: 60%; padding-top: 0px;">
        <div class="layui-card">
          <div><span class="layui-badge layui-bg-blue">我发起的</span></div>
          <div class="layui-card-body">
            <div class="layui-tabs layui-tabs-card main-tabs">
              <ul class="layui-tabs-header">
                <li class="task_type layui-this" data-tab="processing">处理中</li>
                <li class="task_type" data-tab="plan">项目计划</li>
                <li class="task_type" data-tab="work_order">工单</li>
                <li class="task_type" data-tab="risk">风险</li>
              </ul>
              <div class="layui-tabs-body" style="padding: 16px;">
                <div class="layui-tabs-item layui-show">
                  <div class="layui-tabs">
                    <ul class="layui-tabs-header sub-tabs">
                      <li class="layui-this" data-status="all">全部</li>
                      <li data-status="wait_deal">待处理</li>
                      <li data-status="completed">已完成</li>
                    </ul>
                    <div class="layui-tabs-body">
                      <div class="layui-tabs-item layui-show">

                        <div class="layui-card">
                          <div class="layui-card-body">
                            <form class="layui-form layui-row layui-col-space16">
                              <div class="layui-col-md5">
                                <div class="layui-input-wrap">
                                  <input type="text" name="name_cont" id="seach_my_send_name" value="" placeholder="请输入任务名称或任务责任人" class="layui-input" lay-affix="clear">
                                </div>
                              </div>
                              <div class="layui-col-md4">
                                <button class="layui-btn jh-btn-normal" lay-submit lay-filter="search_my-send-table">搜索</button>
                              </div>
                            </form>
                            <table id="my-send-table" lay-filter="my-send-table"></table>
                          </div>
                        </div>

                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="layui-bg-gray" style="width: 60%;">

      </div>
    </div>

  </div>
</div>

<%= javascript_include_tag 'admin/welcome.js'%>
<script>
  $(document).ready(function() {
    initializeTable('data_modules_index', '/admin/welcomes/current_week_data');
    initializeTable('data_modules_index2', '/admin/welcomes/next_week_data');

    // 默认隐藏子选项卡
    $('.sub-tabs').hide();

    // 初始化表格数据
    initTable(false, {tab: 'processing'});

    // 处理子选项卡点击事件
    $(document).off('click', '.sub-tabs li').on('click', '.sub-tabs li', function() {
      var status = $(this).attr('data-status');
      reloadMySendTable();
    });

    // 初始化主选项卡
    $(document).off('click', '.main-tabs .layui-tabs-header .task_type').on('click', '.main-tabs .layui-tabs-header .task_type', function() {
      var tabId = $(this).attr('data-tab');
      if (tabId === 'processing') {
        $('.sub-tabs').hide();
      } else {
        $('.sub-tabs').show();
      }
      $('#seach_my_send_name').val('');
      reloadMySendTable();
    });

  });

</script>