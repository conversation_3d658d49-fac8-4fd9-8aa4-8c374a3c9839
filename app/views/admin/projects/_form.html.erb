<style>
  .project_team_size .layui-input-suffix{
    width: 60px;
  }
</style>
<!-- 联合开发案 -->
<%= simple_form_for([:admin, @project], remote: true, wrapper: :seven_form_line, html: {class: 'layui-form', id: "project_form"}) do |f| %>
  <%= f.error_notification %>
    <% if @project.new_or_edit_status %>
      <%= f.input :commit_status, label: '提交状态', wrapper_html: {style: 'display:none'} %>

      <div class="layui-form string optional">
        <div class="layui-form-item">
          <label class="layui-form-label string optional" for="">项目名称</label>
          <div class="layui-input-block flex-item">
            <input type="radio" name="project[setname]" lay-filter="setname-radio-filter" value="auto" title="自动生成" checked>
            <input type="radio" name="project[setname]" lay-filter="setname-radio-filter" value="customize" title="自定义项目名称" >
          </div>
        </div>
      </div>

      <%= f.input :name, label: '自定义名称', wrapper_html: {'style': 'display: none'} %>
    <% else %>
      <%= f.input :name, label: '项目名称', input_html: {'lay-verify': "required"} %>
    <% end %>
    <%#= f.input :chip_config_id, collection: @chip_config_array, label: '芯片平台', input_html: {'lay-verify': "required"} %>
    <%= f.input :chip_organization_id, label: '芯片来源', wrapper_html: {'style': 'display: none'} %>
    <div class="layui-form string optional">
      <div class="layui-form-item">
        <label class="layui-form-label string optional" for="">芯片平台</label>
        <div class="layui-input-block flex-item">
          <div id="select_chip_config_id" style="width: 99%;" data-value="<%= @chip_config_array.to_json %>"></div>
        </div>
      </div>
    </div>

    <%= f.input :project_type, label: '项目类型', input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'}%>

    <%= f.input :team_size, label: '投入人力', input_html: {'lay-verify': "required"}, textunit: "人/日"%>
    <div class="project-form">
      <%= f.input :product_name, label: '产品名称', input_html: {'lay-verify': "required"}%>
      <%= f.input :product_category_id, collection: @product_category_array, label: '产品类型', input_html: {'lay-verify': "required"} %>
    </div>

    <div class="project-form">
      <%= f.input :evt_at, label: 'EVT时间', input_html: {'lay-verify': "required", class: 'evt_at', autocomplete: 'off', value: f.object.evt_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择EVT时间", readonly: true, unit: "&#xe637;"%>
      <%= f.input :pvt_at, label: 'PVT时间', input_html: {'lay-verify': "required", class: 'pvt_at', autocomplete: 'off', value: f.object.pvt_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择PVT时间", readonly: true, unit: "&#xe637;"%>
    </div>

    <div class="project-form">
      <%= f.input :dvt_at, label: 'DVT时间', input_html: {'lay-verify': "required", class: 'dvt_at', autocomplete: 'off', value: f.object.dvt_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择DVT时间", readonly: true, unit: "&#xe637;"%>
      <%= f.input :mp_at, label: 'MP时间', input_html: {'lay-verify': "required", class: 'mp_at', autocomplete: 'off', value: f.object.mp_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择MP时间", readonly: true, unit: "&#xe637;"%>
    </div>

    <div class="project-form">
      <%= f.input :fcst_per_month, label: 'FCST/月', input_html: {'lay-verify': "required"}, textunit: "K" %>
      <%= f.input :mp_plus_six_months, label: 'MP+6月', input_html: {'lay-verify': "required"}, textunit: "K" %>
    </div>

    <%= f.input :description, as: :text, label: '项目背景', input_html: {'lay-verify': "required", class: 'layui-textarea'} %>
    <%= f.input :main_purpose, label: '主要目的', input_html: {'lay-verify': "required"} %>
    <%= f.input :specification_file, label: '规格附件', input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'} %>
    <div class="layui-form string required project_specification_file">
      <div class="layui-form-item">
        <label class="layui-form-label string required">规格附件</label>
        <div class="layui-input-block flex-item layui-input-wrap">
          <button type="button" class="layui-btn upload-project_specification_file" lay-options="{accept: 'file'}">
            <i class="layui-icon layui-icon-upload"></i>
            上传文件
          </button>
          <div class="layui-inline layui-word-aux file_current">
            <% if @project.persisted? %>
              <span ><%= @project.specification_file.to_filename %><i class='layui-icon layui-icon-clear clear_file' style='color: #ff5722;'></i></span>
            <% end %>
            <a class="down_template" href="javascript:void(0)">下载模板</a>
          </div>
        </div>

      </div>
    </div>

    <div class="project-form">
      <%= f.input :business_contact_id, collection: @users, label: '业务负责人' %>
      <%= f.input :product_manager_id, collection: @users, label: '产品经理', input_html: {'lay-verify': "required"} %>
      <%= f.input :technical_manager_id, collection: @users, label: '技术经理', input_html: {'lay-verify': "required"} %>
    </div>

    <%= f.input :user_id, label: '项目创建人', input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'} %>

  <div class="actions" style='display: none'>
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  BuildFroalaEditor('#project_description');

  var layer, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
      layer = layui.layer,
      form = layui.form;
      var upload = layui.upload;
      var laydate = layui.laydate;
      laydate.render({
        elem: '.design_in_at',
        type: 'datetime',
        fullPanel: true
      });

      laydate.render({
        elem: '.evt_at',
        type: 'datetime',
        fullPanel: true
      });

      laydate.render({
        elem: '.dvt_at',
        type: 'datetime',
        fullPanel: true
      });

      laydate.render({
        elem: '.pvt_at',
        type: 'datetime',
        fullPanel: true
      });

      laydate.render({
        elem: '.mp_at',
        type: 'datetime',
        fullPanel: true
      });

      laydate.render({
        elem: '.opening_at',
        type: 'datetime',
        fullPanel: true
      });

      upload.render({
        elem: '.upload-project_specification_file', // 绑定多个元素
        url: '/upload_anyfile', // 此处配置你自己的上传接口即可
        accept: 'file',
        size: 500000,
        multiple: true,
        done: function(res){
          console.log(res)
          $('.down_template').hide();
          $('#project_specification_file').val(res.link);
          var file_html = `<span >${res.file_name}<i class='layui-icon layui-icon-clear clear_file' style='color: #ff5722;'></i></span>`
          $('.file_current').html('');
          $('.file_current').append(file_html)
        },
        error: function(){ // 上传失败的回调
          layer.msg('文件上传失败', {icon: 2});
        }
      });

      form.on('radio(project_type)', function(data){
        var elem = data.elem; // 获得 radio 原始 DOM 对象
        var checked = elem.checked; // 获得 radio 选中状态
        var value = elem.value; // 获得 radio 值
        var othis = data.othis; // 获得 radio 元素被替换后的 jQuery 对象

        layer.msg(['value: '+ value, 'checked: '+ checked].join('<br>'));
      });

      form.on('radio(setname-radio-filter)', function(data){
        var elem = data.elem; // 获得 radio 原始 DOM 对象
        var checked = elem.checked; // 获得 radio 选中状态
        var value = elem.value; // 获得 radio 值
        var othis = data.othis; // 获得 radio 元素被替换后的 jQuery 对象
        if (value == 'auto') {
          $('#project_name').val('');
          $('.project_name').hide();
        } else {
          $('.project_name').show();
        }
      });

      form.render();
    });
  });

  $(document).on('click', '.clear_file', function(){
    $(this).parent().remove();
    $('#project_specification_file').val('');
    $('.down_template').show();
  });

  $(function () {
    // 初始化 xmSelect 渲染器
    function renderXmSelect(el, name, tips, data, onCallback) {
        layui.xmSelect.render({
            el: el,
            layVerify: 'required',
            name: name,
            autoRow: true,
            radio: true,
            toolbar: { show: true },
            tips: tips,
            filterable: true,
            remoteSearch: false,
            data: data,
            on: onCallback
        });
    }

    // 渲染芯片配置选择器
    renderXmSelect(
        '#select_chip_config_id',
        'project[chip_config_id]',
        '请选择芯片平台',
        JSON.parse($('#select_chip_config_id').attr('data-value')),
        null
    );
  })
</script>