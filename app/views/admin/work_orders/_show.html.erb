<!-- 添加 seven-container 作为作用域容器 -->
<div class="seven-container">
  <div class="seven-flex-container">
    <!-- 左侧详情面板 -->
    <div class="seven-detail-panel">
      <div class="seven-card-header">
        <div class="seven-card-title">
          <span>工单详情</span>
        </div>

        <div class="seven-action-buttons">
          <% @work_order.get_button(current_user).each do |button| %>
            <a class="layui-btn layui-btn-xs layui-btn-normal jh-btn-normal work_orders_<%= button[:id] %>"
               data-uuid="<%= @work_order.id %>"
               data-project_id="<%= @work_order.project_id %>"
               data-event="<%= button[:id]%>">
              <%= button[:title]%>
            </a>
          <% end %>
        </div>
      </div>
      <div class="seven-card-body">
        <div class="seven-ticket-info-grid">
          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:title) %></div>
            <div class="seven-info-value"><%= @work_order.title %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:aasm_state) %></div>
            <div class="seven-info-value">
              <span class="seven-tag seven-tag-blue"><%= t("workflows.work_order.aasm_state.#{@work_order.aasm_state}") %></span>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:priority) %></div>
            <div class="seven-info-value">
              <span class="seven-tag seven-tag-red"><%= @work_order.priority_i18n %></span>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:customer_name) %></div>
            <div class="seven-info-value"><%= @work_order.customer_name %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">创建人</div>
            <div class="seven-info-value">
              <div style="display: flex; align-items: center; gap: 8px;">
                <div class="seven-comment-avatar" style="width: 24px; height: 24px; font-size: 12px;">
                  <%= @work_order.founder.name.to_s.first %>
                </div>
                <span><%= @work_order.founder.name %></span>
              </div>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:receiver_user_id) %></div>
            <div class="seven-info-value">
              <div style="display: flex; align-items: center; gap: 8px;">
                <div class="seven-comment-avatar" style="width: 24px; height: 24px; font-size: 12px;">
                  <%= (@work_order.receiver_user&.name || '无').to_s.first %>
                </div>
                <span><%= @work_order.receiver_user&.name %></span>
              </div>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">任务处理人</div>
            <div class="seven-info-value">
              <div style="display: flex; align-items: center; gap: 8px;">
                <div class="seven-comment-avatar" style="width: 24px; height: 24px; font-size: 12px;">
                  <%= (@work_order.obligation_user&.name || '无').to_s.first  %>
                </div>
                <span><%= @work_order.obligation_user&.name %></span>
              </div>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:product_name) %></div>
            <div class="seven-info-value"><%= @work_order.product_name %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:hardware_version) %>:</div>
            <div class="seven-info-value"><%= @work_order.hardware_version %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:which_module) %></div>
            <div class="seven-info-value"><%= @work_order.which_module %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:problem_severity) %></div>
            <div class="seven-info-value"><%= @work_order.problem_severity_i18n %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">平台共性</div>
            <div class="seven-info-value"><%= @work_order.is_platform_commonality ? t("workflows.work_order.is_platform_commonality.#{@work_order.is_platform_commonality}") : nil %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">芯片平台</div>
            <div class="seven-info-value"><%= @work_order.chip_config&.name %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">OS软件</div>
            <div class="seven-info-value"><%= @work_order.chip_os_software&.name %></div>
          </div>
          <div class="seven-info-item">
            <div class="seven-info-label">软件版本</div>
            <div class="seven-info-value"><%= @work_order.chip_os_version&.version %></div>
          </div>
          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:product_category_id) %></div>
            <div class="seven-info-value"><%= @work_order.product_category&.name %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:demand_sources) %></div>
            <div class="seven-info-value"><%= @work_order.demand_sources_i18n %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:started_at) %></div>
            <div class="seven-info-value"><%= @work_order.started_at&.strftime("%F %T") %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:ended_at) %></div>
            <div class="seven-info-value"><%= @work_order.ended_at&.strftime("%F %T") %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:act_started_at) %></div>
            <div class="seven-info-value"><%= @work_order.act_started_at&.strftime("%F %T") %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:act_ended_at) %></div>
            <div class="seven-info-value"><%= @work_order.act_ended_at&.strftime("%F %T") %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:closed_at) %></div>
            <div class="seven-info-value"><%= @work_order.closed_at&.strftime("%F %T") %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label"><%= WorkOrder.human_attribute_name(:project_progress) %></div>
            <div class="seven-info-value"><%= @work_order.project_progress_i18n %></div>
          </div>
        </div>

        <hr style="margin: 16px 0; border-color: rgba(0, 0, 0, 0.06);">

        <div class="seven-info-item">
          <div class="seven-info-label">问题描述</div>
          <div class="seven-description-box">
            <%= @work_order.description&.html_safe %>
          </div>
        </div>

        <div class="seven-info-item">
          <div class="seven-info-label">重现步骤</div>
          <div class="seven-description-box">
            <%= @work_order.repro_steps&.html_safe %>
          </div>
        </div>

        <div class="seven-info-item">
          <div class="seven-info-label">附件</div>
          <div style="display: flex; gap: 8px; margin-top: 8px;">
            <% if @work_order.file.present? %>
              <a href="<%= @work_order.file %>" class="seven-file-attachment" target="_blank">
                <i class="layui-icon layui-icon-file" style="color: #3498db;"></i>
                <%= @work_order.file.to_filename %>
              </a>
            <% else %>
              <span>无附件</span>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧面板 -->
    <div class="seven-side-panel">

      <div class="seven-tabs">
        <!-- 标签导航 -->
        <ul class="seven-tabs-nav">
          <li class="seven-tabs-tab seven-tabs-tab-active">评论</li>
          <li class="seven-tabs-tab">变动记录</li>
        </ul>
        <div class="seven-tabs-content">
          <!-- 评论卡片 -->
          <div id="<%= @commentable_type %>_<%= @commentable_id %>_comments" class="seven-tabs-pane seven-tabs-pane-active" style="overflow: hidden;">
            <%= render 'admin/comments/show' %>
          </div>

          <!-- 变动记录面板 -->
          <div class="seven-tabs-pane">
            <div class="seven-card">
              <div class="seven-card-body">
                <div class="seven-timeline">
                  <% @work_order.build_change_log.each do |log| %>
                    <div class="seven-timeline-item">
                      <div class="seven-timeline-dot"></div>
                      <div class="seven-timeline-date"><%= log[:change_at] %></div>
                      <div class="seven-timeline-content">
                        <% if log[:change_type]=='create' %>
                          <p>创建任务</p>
                        <% else %>
                          <% log[:changes].each do |change| %>
                            <% if change[:from].present? %>
                              <p>
                                <span style='color: #1677ff'>
                                  <%= change[:field] %>
                                </span>: <br>
                                由 "<span style='color: #faad14'>
                                  <%= change[:from] %>
                                </span>"
                                变动为"<span style='color: #52c41a'>
                                  <%= change[:to] %>
                                </span>"
                              </p>
                            <% else %>
                              <span style='color: #1677ff'>
                                <%= change[:field] %>
                              </span>:<br>变动为
                              "<span style='color: #52c41a'>
                                <%= change[:to] %>
                              </span>"<br>
                            <% end %>
                          <% end %>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>

    </div>
  </div>
</div>

<%= javascript_include_tag 'admin/welcome.js'%>
