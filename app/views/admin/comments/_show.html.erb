<!-- 评论卡片 -->
<div class="seven-card" style="display: flex; flex-direction: column; height: 100%;">
  <span id="at_user_array"><%= @at_user_array %></span>
  <div class="seven-card-body" style="display: flex; flex-direction: column; padding: 0px 16px; flex: 1; overflow: hidden;">
    <div id="seven-comments-container" style="flex: 1; overflow-y: auto; padding-bottom: 16px;">
      <!-- 循环渲染根评论 -->
      <% @comment_tree.each do |comment| %>
        <%= render 'admin/comments/comment', comment: comment %>
      <% end %>
      <% if @comment_tree.empty? %>
        <div class="seven-empty-state">
          <div class="seven-empty-icon">💬</div>
          <div class="seven-empty-text">暂无评论，发表第一条评论吧</div>
        </div>
      <% end %>
    </div>

    <!-- 固定在底部的评论输入框 -->
    <div class="seven-comment-form" style="margin-top: auto; border-top: 1px solid rgba(0, 0, 0, 0.06); padding-top: 16px;">
      <div class="seven-reply-indicator" style="display: none; margin-bottom: 8px; font-size: 12px; color: #1677ff;">
        回复给：<span id="seven-reply-to-user"></span>
        <button id="seven-cancel-reply" style="margin-left: 8px; color: #999; background: none; border: none; cursor: pointer;">取消</button>
      </div>
      <textarea id="seven-comment-input" placeholder="在此输入您的评论..." data-mention-users="<%= @initial_mention_users.to_json %>"></textarea>
      <div style="display: flex; justify-content: flex-end;">
        <button id="seven-submit-comment" style="padding: 4px 16px; background-color: #1677ff; color: white; border: none; border-radius: 4px; cursor: pointer;">提交评论</button>
      </div>
    </div>
  </div>
</div>


<script>
  // 获取评论对象信息（根据你的业务调整）
  $(document).ready(function() {
    var commentableType = '<%= @commentable_type %>';
    var commentableId = '<%= @commentable_id %>';
    var initialMentionUsers = JSON.parse($('#seven-comment-input').attr('data-mention-users'));
    console.log(initialMentionUsers);

    var comment_editor = commentCkeditor('#seven-comment-input', {initialMentionUsers: initialMentionUsers});

    var parentId = ''; // 初始化 parent_id

    // 回复按钮点击事件
    $(document).off('click', '.seven-reply-btn').on('click', '.seven-reply-btn', function () {
      var userName = $(this).data('user');
      $('#seven-reply-to-user').text(userName);
      $('.seven-reply-indicator').show();
      $('#seven-comment-input').attr('placeholder', '回复给 ' + userName + '...').focus();
      // 更新 parentId 为当前被回复的评论 ID
      $('#seven-submit-comment').attr('data-id', $(this).data('id'))
    });

    // 取消回复按钮点击事件
    $(document).off('click', '#seven-cancel-reply').on('click', '#seven-cancel-reply', function () {
      $('.seven-reply-indicator').hide();
      $('#seven-comment-input').attr('placeholder', '在此输入您的评论...');
      $('#seven-submit-comment').attr('data-id', '')
    });

    // 提交评论功能
    $(document).off('click', '#seven-submit-comment').on('click', '#seven-submit-comment', function () {
      // 获取编辑器内容
      comment_editor.then(editor => {
        const content = editor.getData();
        console.log('Editor content:', content);

        // ✅ 判断内容是否为空
        if (isContentEmpty(content)) {
          layer.msg('请输入评论内容');
          return;
        }

        // 提取 user-id
        var $content = $(content);
        var userIds = $content.find('span.mention[data-mention][data-user-id]').map(function() {
          return $(this).data('user-id');
        }).get();

        console.log('User IDs:', userIds);

        var replyTo = $('#seven-reply-to-user').text();
        var isReply = replyTo !== '';

        $.ajax({
          url: '/admin/comments', // 替换为你的后端接口
          method: 'POST',
          data: {
            comment: {
              content: content,
              commentable_type: commentableType,
              commentable_id: commentableId,
              parent_id: $('#seven-submit-comment').attr('data-id'),
              at_user_ids: userIds
            }
          }
        });
        $('.seven-empty-state').remove();

      })
    });
  });

  // 删除
  $(document).off('click', '.seven-delete-btn').on('click', '.seven-delete-btn', function () {
    var id = $(this).data('id');
    layer.confirm("确认此评论吗，删除后子评论也会连带删除？", function(index){
      $.ajax({
        type: 'DELETE',
        url: `/admin/comments/${id}`,
        data: {
          commentable_type: commentableType,
          commentable_id: commentableId,
        }
      })
      layer.close(index);
    });
  });

</script>