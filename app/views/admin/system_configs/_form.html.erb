<%= simple_form_for([:admin, @system_config], wrapper: :seven_form, html: {id: 'user_form', class: 'layui-form', multipart: true, novalidate: 'novalidate' }) do |f| %>
  <%= f.error_notification %>

    <%= f.input :feishu_app_id, label: '飞书 AppId' %>
    <%= f.input :feishu_app_secret, label: '飞书 AppSecret' %>
    <%#= f.input :ding_agent_id, label: '钉钉 AgentId' %>
    <%#= f.input :ding_app_key, label: '钉钉 AppKey' %>
    <%#= f.input :ding_app_secret, label: '钉钉 AppSecret' %>
    <%#= f.input :qiye_app_id, label: '企微 AppId' %>
    <%#= f.input :qiye_agent_id, label: '企微 AgentId' %>
    <%#= f.input :qiye_secret, label: '企微 Secret' %>

  <div class="actions">
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
  </div>
<% end %>

<script>
  var layer, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
      layer = layui.layer,
      form = layui.form;
      form.render();
    });
  });
</script>