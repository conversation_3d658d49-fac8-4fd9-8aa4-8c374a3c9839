<!-- 添加 seven-container 作为作用域容器 -->
<div class="seven-container">
  <div class="seven-flex-container">
    <!-- 左侧详情面板 -->
    <div class="seven-detail-panel">
      <div class="seven-card-header">
        <div class="seven-card-title">
          <span>计划详情</span>
        </div>

        <div class="seven-action-buttons">
          <% @plan.get_button(current_user).each do |button| %>
            <a class="layui-btn layui-btn-xs layui-btn-normal jh-btn-normal plans_<%= button[:id] %>" data-uuid="<%= @plan.id %>" data-project_id="<%= @plan.project_id %>" data-event="<%= button[:id]%>"><%= button[:title]%></a>
          <% end %>
        </div>
      </div>
      <div class="seven-card-body">
        <div class="seven-ticket-info-grid">
          <div class="seven-info-item">
            <div class="seven-info-label">名称</div>
            <div class="seven-info-value"><%= @plan.name %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">状态</div>
            <div class="seven-tag seven-tag-blue"><%= @plan.status_i18n %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">类型</div>
            <div class="seven-info-value"><%= @plan.p_type_i18n %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">优先级</div>
            <div class="seven-info-value"><%= @plan.p_priority_i18n %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">父级任务</div>
            <div class="seven-info-value"><%= @plan.parent_plan&.name %></div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">所属项目</div>
            <div class="seven-info-value">
              <span class="seven-info-value"><%= @plan.project.name %></span>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">责任人</div>
            <div class="seven-info-value">
              <div style="display: flex; align-items: center; gap: 8px;">
                <div class="seven-comment-avatar" style="width: 24px; height: 24px; font-size: 12px;">
                  <%= @plan.duty_user&.name.to_s.first %>
                </div>
                <span><%= @plan.duty_user&.name %></span>
              </div>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">创建人</div>
            <div class="seven-info-value">
              <div style="display: flex; align-items: center; gap: 8px;">
                <div class="seven-comment-avatar" style="width: 24px; height: 24px; font-size: 12px;">
                  <%= @plan.user&.name.to_s.first %>
                </div>
                <span><%= @plan.user.name %></span>
              </div>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">计划开始时间</div>
            <div class="seven-info-value">
              <span class="seven-info-value"><%= @plan.started_at.strftime('%F %T') %></span>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">计划结束时间</div>
            <div class="seven-info-value">
              <span class="seven-info-value"><%= @plan.ended_at.strftime('%F %T') %></span>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">实际开始时间</div>
            <div class="seven-info-value">
              <span class="seven-info-value"><%= @plan.act_started_at&.strftime('%F %T') %></span>
            </div>
          </div>

          <div class="seven-info-item">
            <div class="seven-info-label">实际结束时间</div>
            <div class="seven-info-value"><%= @plan.act_ended_at&.strftime('%F %T') %></div>
          </div>

        </div>

        <hr style="margin: 16px 0; border-color: rgba(0, 0, 0, 0.06);">

        <div class="seven-info-item">
          <div class="seven-info-label">内容描述</div>
          <div class="seven-description-box">
            <%= @plan.content&.html_safe %>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧面板 -->
    <div class="seven-side-panel">
      <div class="seven-tabs">
        <!-- 标签导航 -->
        <ul class="seven-tabs-nav">
          <li class="seven-tabs-tab seven-tabs-tab-active">评论</li>
          <li class="seven-tabs-tab">变动记录</li>
        </ul>
        <div class="seven-tabs-content">
          <div id="<%= @commentable_type %>_<%= @commentable_id %>_comments" class="seven-tabs-pane seven-tabs-pane-active" style="overflow: hidden;">
            <%= render 'admin/comments/show' %>
          </div>

          <!-- 变动记录面板 -->
          <div class="seven-tabs-pane">
            <div class="seven-card">
              <div class="seven-card-body">
                <div class="seven-timeline">
                  <% @plan.build_change_log.each do |log| %>
                    <div class="seven-timeline-item">
                      <div class="seven-timeline-dot"></div>
                      <div class="seven-timeline-date"><%= log[:change_at] %></div>
                      <div class="seven-timeline-content">
                        <% if log[:change_type]=='create' %>
                          <p>创建任务</p>
                        <% else %>
                          <% log[:changes].each do |change| %>
                            <% if change[:from].present? %>
                              <p>
                                <span style='color: #1677ff'>
                                  <%= change[:field] %>
                                </span>: <br>
                                由 "<span style='color: #faad14'>
                                  <%= change[:from] %>
                                </span>"
                                变动为"<span style='color: #52c41a'>
                                  <%= change[:to] %>
                                </span>"
                              </p>
                            <% else %>
                              <span style='color: #1677ff'>
                                <%= change[:field] %>
                              </span>:<br>变动为
                              "<span style='color: #52c41a'>
                                <%= change[:to] %>
                              </span>"<br>
                            <% end %>
                          <% end %>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<%= javascript_include_tag 'admin/welcome.js'%>
