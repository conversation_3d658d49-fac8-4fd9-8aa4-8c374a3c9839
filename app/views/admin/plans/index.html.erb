<div class="layui-card">
  <div class="layui-card-body">
    <%= render 'admin/projects/project_tab' %>
    <div style="padding: 6px 0px; font-size: 13px;">
      项目发布状态: <span style="color: <%= @show_release_plans[0] ? '#16b777;' : '#ff5722;'%>"><%= @show_release_plans[1] %></span>
    </div>
    <table id="table_plans" lay-filter="admin-table"></table>
  </div>
</div>

<!-- <script type="text/html" id="barDemo">
  <a class="layui-btn layui-btn-xs layui-border-blue layui-btn-primary" lay-event="add"> <i class="layui-icon layui-icon-addition"></i></a>
  <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="edit"> <i class="layui-icon layui-icon-edit"></i></a>
  <a class="layui-btn layui-btn-xs layui-border-orange layui-btn-primary" lay-event="detail"><i class="layui-icon layui-icon-eye"></i></a>
  <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="deleted"> <i class="layui-icon layui-icon-delete"></i> </a>
</script> -->

<script type="text/html" id="myBar">
  <% if @project_power_keys.include?('add_plan') %>
    <button lay-event="add" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-add-1"></i>新增</button>
    <button lay-event="down_excel" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-download-circle"></i>模版</button>
    <button lay-event="import_excel" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-templeate-1"></i>导入</button>
  <% end %>

  <button lay-event="expand" type="button" class="layui-btn layui-btn-primary layui-btn-sm"><i class="layui-icon layui-icon-up"></i>展开全部</button>
</script>

<script>
  var treeTable;
  layui.use(function(){
    treeTable = layui.treeTable
    var layer = layui.layer

    var default_toolbar_status = `<%= @project_power_keys.include?('export_plan') %>`
    default_toolbar = ['filter', 'print']
    if (default_toolbar_status == 'true'){
      default_toolbar = [
        'filter',
        {
          name: 'exports',
          onClick: function(obj) {
            obj.openPanel({
              list: [ // 列表
                '<li data-type="xlsx">导出项目计划-xlsx</li>'
              ].join(''),
              done: function(panel, list) { // 操作列表
                list.on('click', function() {
                  var type = $(this).data('type')
                  if(type === 'xlsx') {
                    window.location.href = `/admin/plans?excel=true&project_id=<%= @project.id %>`
                  }
                });
              }
            });
          }
        },
        'print'
      ]
    }


    treeTable.render({
      id: 'listPage',
      elem: '#table_plans',
      url: `/admin/plans?project_id=<%= @project.id %>`, //数据接口
      title: '列表',
      skin: 'line',
      page: false, //开启分页
      toolbar: '#myBar',
      pid: 'parent_id',
      isParent: 'is_parent',
      isSimpleData: true,
      defaultToolbar: default_toolbar,
      cols: [[
          {field: 'order_number', align:'left', title: '序号', width: 50, fixed: 'left'},

          {field: 'name', align: 'left', title: '标题', minWidth: 300, fixed: 'left', templet: function (d) {
            var tag = '';
            if (d.daily_status_tag.length > 0){
              var tag = `<span class="seven-tag ${d.daily_status_tag[1]}">${d.daily_status_tag[0]}</span>`
            }
            return `<span>${d.name}${tag}</span>`;
          }},

          {field: 'p_type', align:'left', title: '类型', width: 100},

          {field: 'started_at', align:'left', title: '计划开始时间', width: 180},

          {field: 'ended_at', align:'left', title: '计划结束时间', width: 180},

          {field: 'status', align:'left', title: '状态', width: 80},

          {field: 'p_priority', align:'left', title: '优先级', minWidth: 100},

          {field: 'user_name', align:'left', title: '发起人', minWidth: 100},

          {field: 'duty_user_name', align:'left', title: '责任人', minWidth: 100},

        // {fixed: 'right', title: '操作', minWidth: 200, align: 'left', toolbar: '#barDemo'}

          {fixed: 'right', align: 'left', title: '操作', minWidth: 180, templet: function (d)
            {
              var button = '';
              d.button.forEach(res => {
                button += `<a class="layui-btn layui-btn-xs ${res.color} layui-btn-primary" lay-event="${res.id}">${res.title}</a>`
              });
              return button;
            }
          }

      ]]
    });

    //监听行工具事件
	  treeTable.on('tool(admin-table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
      var data = obj.data //获得当前行数据
      ,layEvent = obj.event; //获得 lay-event 对应的值
      if(layEvent === 'edit'){
        $.ajax({
          type: 'GET',
          url: `/admin/plans/${data.id}/edit`,
          data: {
            project_id: `<%= @project.id %>`
          }
        })
      }else if (layEvent === 'detail'){
        $.ajax({
          type: 'GET',
          url: `/admin/plans/${data.id}`,
          data: {
            project_id: `<%= @project.id %>`
          }
        })
      }else if (layEvent === 'deleted'){
        var ids = treeTable.getData('listPage', true).map(i => i.id)

        layer.confirm('确认删除吗？此操作不可恢复', function(index){
          layer.close(index);
          $.ajax({
            type: 'DELETE',
            url: `/admin/plans/${data.id}`,
            data: {
              project_id: `<%= @project.id %>`,
              sort_ids: ids
            }
          })
        })
      }else if (layEvent === 'add'){
        $.ajax({
          type: 'GET',
          url: `/admin/plans/new`,
          data: {
            project_id: `<%= @project.id %>`,
            parent_id: data.id
          }
        })
      }

	  });

    //监听头工具栏事件
    treeTable.on('toolbar(admin-table)', function(obj){
      var checkStatus = treeTable.checkStatus(obj.config.id)
      ,data = checkStatus.data; //获取选中的数据
      switch(obj.event){
        case 'add':
          $.ajax({
            type: 'GET',
            url: `/admin/plans/new`,
            data: {
              project_id: `<%= @project.id %>`
            }
          })
          break;
        case 'expand':
          $(this).find('i').toggleClass('layui-icon-down');
          $(this).find('i').toggleClass('layui-icon-up');
          layui.treeTable.expandAll('listPage', $(this).find('i').hasClass('layui-icon-down')); // 关闭全部节点
          break;
        case 'down_excel':
          window.location.href = `/admin/plans/down_excel`;
          break;
        case 'import_excel':
          var project_id = `<%= @project.id %>`
          layer.open({
            type: 1, // page 层类型
            area: ['40%', '50%'],
            title: '导入项目计划',
            resize: false,
            btn: ['上传'],
            yes:function(){
              $('#ID-upload-demo-action').click();
            },
            id: 'show-mechanism',
            maxmin: false, // 允许全屏最小化
            content: `<div class="layui-upload-drag" style="display: block; height: 75%;" id="ID-upload-demo-choose">
                        <div id="notice-uploder">
                          <svg style="display: inline-block;" t="1724749540604" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="17517" width="65" height="65"><path d="M554.666667 550.4V768h192c85.333333-8.533333 149.333333-81.066667 149.333333-170.666667 0-93.866667-76.8-170.666667-170.666667-170.666666h-4.266666c-21.333333-98.133333-106.666667-170.666667-209.066667-170.666667-119.466667 0-213.333333 93.866667-213.333333 213.333333-72.533333 8.533333-128 72.533333-128 149.333334C170.666667 699.733333 238.933333 768 320 768H512v-217.6l-98.133333 98.133333-29.866667-29.866666 149.333333-149.333334 149.333334 149.333334-29.866667 29.866666-98.133333-98.133333z m0 217.6v42.666667h-42.666667v-42.666667h-64v42.666667h-128C213.333333 810.666667 128 725.333333 128 618.666667c0-85.333333 55.466667-157.866667 128-183.466667C273.066667 311.466667 379.733333 213.333333 512 213.333333c110.933333 0 209.066667 72.533333 243.2 170.666667 102.4 12.8 183.466667 102.4 183.466667 213.333333s-85.333333 200.533333-192 213.333334h-128v-42.666667H554.666667z" fill="#444444" p-id="17518"></path></svg>
                          <div>点击上传或将文件拖拽到此处 <br><span style="font-size: 12px;">支持扩展名 .xlsx</span></div>
                        </div>
                        <div class="layui-hide" id="ID-upload-demo-preview">
                          <svg style="width: 40px; height: 40px; display: inline-block;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" fill="none" class="h-10 w-10 flex-shrink-0" width="36" height="36"><rect width="36" height="36" rx="6" fill="#FF5588"></rect><path d="M19.6663 9.66663H12.9997C12.5576 9.66663 12.1337 9.84222 11.8212 10.1548C11.5086 10.4673 11.333 10.8913 11.333 11.3333V24.6666C11.333 25.1087 11.5086 25.5326 11.8212 25.8451C12.1337 26.1577 12.5576 26.3333 12.9997 26.3333H22.9997C23.4417 26.3333 23.8656 26.1577 24.1782 25.8451C24.4907 25.5326 24.6663 25.1087 24.6663 24.6666V14.6666L19.6663 9.66663Z" stroke="white" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"></path><path d="M19.667 9.66663V14.6666H24.667" stroke="white" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"></path><path d="M21.3337 18.8334H14.667" stroke="white" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"></path><path d="M21.3337 22.1666H14.667" stroke="white" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"></path><path d="M16.3337 15.5H15.5003H14.667" stroke="white" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"></path></svg>
                          <div id="uploade-filename" style="margin-top: 10px;"></div>
                        </div>
                      </div>
                      <div style='display: none; padding-right: 10px; text-align: right; padding-top: 30px;'><a class="layui-btn layui-bg-blue layui-btn-sm" id="ID-upload-demo-action">上传</a></div>
                      `,
            success:function(){
              layui.use(function(){
                var upload = layui.upload;
                // 渲染
                upload.render({
                  elem: '#ID-upload-demo-choose',
                  url: `/admin/plans/import_excel?project_id=${project_id}`, // 此处配置你自己的上传接口即可
                  auto: false,
                  bindAction: '#ID-upload-demo-action',
                  accept: 'file',
                  before: function(obj){
                    layer.load(2, {shade: 0.1, time: 30*1000});
                  },
                  done: function(res){
                    console.log(res)
                    layer.closeAll();
                    if (res.status){
                      if (res.data.error_count > 0){
                        layer.confirm(`导入完毕 错误信息文件: <a href='${res.data.url}' style='color: #2468f2'>下载: ${res.data.filename}</a>`, {icon: 1, btn: ['关闭']});
                      }else{
                        layer.closeAll();
                        layer.msg("导入成功", {icon: 1});
                        treeTable.reload("listPage")
                      }
                    }else{
                      layer.msg(res.msg);
                    }
                  },
                  choose: function(obj){
                    // 预读本地文件，如果是多文件，则会遍历。(不支持ie8/9)
                    obj.preview(function(index, file, result){
                      $('#notice-uploder').addClass('layui-hide');
                      $('#ID-upload-demo-preview').removeClass('layui-hide').find('#uploade-filename').text(file.name);
                    });
                  }
                });
              });
            }
          });
          break;
      };
    });
  })

  function updateSort(){
    var ids = treeTable.getData('listPage', true).map(i => i.id)
    $.ajax({
      type: 'POST',
      url: `/admin/plans/update_sort`,
      data: {
        project_id: `<%= @project.id %>`,
        sort_ids: ids
      },
      success:function(res){
        if (!res.status){
          layer.msg('排序更新失败，请联系管理员查询问题');
        }
      }
    })
  }
</script>