# == Route Map
#

Rails.application.routes.draw do
  get '/admin' => "smart_alliance/law_admins#index"
  get '/' => "smart_alliance/law_admins#index"

  post "/upload_file" => "upload#upload_file", :as => :upload_file
  post "/upload_video" => "upload#upload_video", :as => :upload_video
  post "/upload_image" => "upload#upload_image", :as => :upload_image
  post "/upload_anyfile" => "upload#upload_anyfile", :as => :upload_anyfile

  get "/download_file/:name" => "upload#access_file", :as => :upload_access_file, :name => /.*/

  get "/oauth2/login" => "oauth2_sessions#login", :as => :oauth2_login
  get "/oauth2/callback" => "oauth2_sessions#callback", :as => :oauth2_callback
  post "/oauth2/logout" => "oauth2_sessions#logout", :as => :oauth2_logout

  namespace :smart_alliance do
    resources :law_admins
  end

  namespace :web do
  end

  namespace :admin do
    resources :system_configs
    resources :project_risks do
      member do
        post :change_state
        get :details
      end
    end
    resources :project_organizations
    resources :flow_steps
    resources :approval_steps, only: [:show]
    resources :flow_versions
    resources :organization_flows
    resources :plans do
      collection do
        post :update_sort
        post :import_excel
        get :search_parent_time
        get :down_excel
      end
      member do
        get :details
      end
    end

    resources :roles
    resources :groups do
      member do
        post :add_user
        delete :remove_user
      end
      collection do
        get :available_users
      end
    end
    resources :project_users do
      collection do
        post :delete_role
        post :update_user_role
      end
    end
    resources :public_api do
      collection do
        get :get_chip_sofwares
        get :get_chip_versions
        get :search_users
        get :get_chip_configs
        get :search_project_users
        get :mention_users
      end
    end

    resources :work_orders do
      collection do
        get :evaluate_work_orders
        get :passing_work_orders
        get :project_bug_works
        get :project_demand_works
      end
      member do
        post :change_state
        get :details
      end
    end
    resources :organizations do
      member do
        get :permission_controllers
        post :assign_permission_controller
        delete :remove_permission_controller
        get :users
      end
    end
    resources :chip_os_versions
    resources :chip_os_softwares do
      get :get_version, on: :collection
    end

    resources :chip_configs
    resources :product_categories
    resources :projects do
      collection do
        get :new_joint
        get :new_support
        get :edit_joint
        get :edit_support
        post :update_status
        get :admin
      end
    end
    resources :project_role_configs do
      post :update_is_default, on: :collection
    end

    resources :users do
      collection do
        post :sync_feishu
      end
    end

    resources :welcomes do
      collection do
        get :approval_step_users_list
        post :update_approval_step_user
        get :current_week_data
        get :next_week_data
        post :update_plan_status
        post :update_work_order_status
        get :projects
        post :update_project_risk_status
        get :my_send
      end
    end
    resources :sessions do
      collection do
        post :logout
        post :login_user
        get :login
        post :update_password
      end
    end
    resources :user_notices
    resources :comments
  end
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Defines the root path route ("/")
  # root "articles#index"

  namespace :api, defaults: {format: :json} do
    namespace :v20240522 do
      resources :users
    end
  end
end
