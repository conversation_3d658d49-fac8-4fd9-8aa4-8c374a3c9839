require 'test_helper'

class Admin::SystemConfigsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @system_config = system_configs(:one)
  end

  test "should get index" do
    get admin_system_configs_url
    assert_response :success
  end

  test "should get new" do
    get new_admin_system_config_url
    assert_response :success
  end

  test "should create system_config" do
    assert_difference('SystemConfig.count') do
      post admin_system_configs_url, params: { system_config: { ding_agent_id: @system_config.ding_agent_id, ding_app_key: @system_config.ding_app_key, ding_app_secret: @system_config.ding_app_secret, feishu_app_id: @system_config.feishu_app_id, feishu_app_secret: @system_config.feishu_app_secret, organization_id: @system_config.organization_id, qiye_agent_id: @system_config.qiye_agent_id, qiye_app_id: @system_config.qiye_app_id, qiye_secret: @system_config.qiye_secret } }
    end

    assert_redirected_to admin_system_config_url(SystemConfig.last)
  end

  test "should show system_config" do
    get admin_system_config_url(@system_config)
    assert_response :success
  end

  test "should get edit" do
    get edit_admin_system_config_url(@system_config)
    assert_response :success
  end

  test "should update system_config" do
    patch admin_system_config_url(@system_config), params: { system_config: { ding_agent_id: @system_config.ding_agent_id, ding_app_key: @system_config.ding_app_key, ding_app_secret: @system_config.ding_app_secret, feishu_app_id: @system_config.feishu_app_id, feishu_app_secret: @system_config.feishu_app_secret, organization_id: @system_config.organization_id, qiye_agent_id: @system_config.qiye_agent_id, qiye_app_id: @system_config.qiye_app_id, qiye_secret: @system_config.qiye_secret } }
    assert_redirected_to admin_system_config_url(SystemConfig.last)
  end

  test "should destroy system_config" do
    assert_difference('SystemConfig.count', -1) do
      delete admin_system_config_url(@system_config)
    end

    assert_redirected_to admin_system_configs_url
  end
end
