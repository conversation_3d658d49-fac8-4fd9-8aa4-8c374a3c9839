# == Schema Information
#
# Table name: group_users
#
#  id                      :bigint           not null, primary key
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  group_id(用户组ID)      :integer
#  organization_id(组织ID) :integer
#  user_id(用户ID)         :integer
#
# Indexes
#
#  index_group_users_on_group_and_user   (group_id,user_id) UNIQUE
#  index_group_users_on_group_id         (group_id)
#  index_group_users_on_organization_id  (organization_id)
#  index_group_users_on_user_id          (user_id)
#
require "test_helper"

class GroupUserTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
