# == Schema Information
#
# Table name: system_configs
#
#  id                              :bigint           not null, primary key
#  deleted_at(删除时间)            :datetime
#  ding_app_key(钉钉应用Key)       :string
#  ding_app_secret(钉钉应用密钥)   :string
#  feishu_app_secret(飞书应用密钥) :string
#  qiye_secret(企微应用Secret)     :string
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  ding_agent_id(钉钉应用ID)       :string
#  feishu_app_id(飞书应用ID)       :string
#  organization_id(组织ID)         :integer
#  qiye_agent_id(企微应用AgentID)  :string
#  qiye_app_id(企微应用ID)         :string
#
# Indexes
#
#  index_system_configs_on_organization_id  (organization_id)
#
require "test_helper"

class SystemConfigTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
