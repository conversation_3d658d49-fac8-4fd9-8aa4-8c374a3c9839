# == Schema Information
#
# Table name: groups
#
#  id                      :bigint           not null, primary key
#  description(用户组描述) :text
#  name(用户组名称)        :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  organization_id(组织ID) :integer
#
# Indexes
#
#  index_groups_on_organization_id  (organization_id)
#
require "test_helper"

class GroupTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
