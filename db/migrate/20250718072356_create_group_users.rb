class CreateGroupUsers < ActiveRecord::Migration[7.0]
  def change
    create_table :group_users do |t|
      t.integer :group_id, comment: "用户组ID"
      t.integer :user_id, comment: "用户ID"
      t.integer :organization_id, comment: "组织ID"

      t.timestamps
    end
    add_index :group_users, :group_id
    add_index :group_users, :user_id
    add_index :group_users, :organization_id
    add_index :group_users, [:group_id, :user_id], unique: true, name: 'index_group_users_on_group_and_user'
  end
end
