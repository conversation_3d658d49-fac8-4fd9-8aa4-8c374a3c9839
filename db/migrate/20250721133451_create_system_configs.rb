class CreateSystemConfigs < ActiveRecord::Migration[7.0]
  def change
    create_table :system_configs do |t|
      t.integer :organization_id, comment: '组织ID'
      t.string :feishu_app_id, comment: '飞书应用ID'
      t.string :feishu_app_secret, comment: '飞书应用密钥'
      t.string :ding_agent_id, comment: '钉钉应用ID'
      t.string :ding_app_key, comment: '钉钉应用Key'
      t.string :ding_app_secret, comment: '钉钉应用密钥'
      t.string :qiye_app_id, comment: '企微应用ID'
      t.string :qiye_agent_id, comment: '企微应用AgentID'
      t.string :qiye_secret, comment: '企微应用Secret'
      t.datetime :deleted_at, comment: '删除时间'

      t.timestamps
    end
    add_index :system_configs, :organization_id
  end
end
