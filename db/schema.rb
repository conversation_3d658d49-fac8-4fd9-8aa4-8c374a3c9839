# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_07_21_133451) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "approval_flows", force: :cascade do |t|
    t.integer "organization_flow_id", comment: "流程ID"
    t.string "flowable_type"
    t.bigint "flowable_id", comment: "多态"
    t.integer "current_step_id", comment: "当前步骤ID"
    t.integer "status", comment: "状态(1: \"进行中\", 2: \"已完成\", 3: \"驳回\")"
    t.datetime "deleted_at", comment: "删除时间"
    t.integer "organization_id", comment: "组织ID"
    t.integer "user_id", comment: "用户ID、发起人ID"
    t.boolean "is_effect", comment: "是否生效"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["flowable_type", "flowable_id"], name: "index_approval_flows_on_flowable"
    t.index ["organization_flow_id"], name: "index_approval_flows_on_organization_flow_id"
    t.index ["organization_id"], name: "index_approval_flows_on_organization_id"
    t.index ["user_id"], name: "index_approval_flows_on_user_id"
  end

  create_table "approval_step_users", force: :cascade do |t|
    t.integer "order_number", comment: "步骤顺序"
    t.integer "user_id", comment: "审核人用户ID"
    t.integer "approval_step_id", comment: "审核步骤ID"
    t.integer "status", comment: "状态(1: 等待中, 2: \"进行中\", 3: \"已完成\", 4: \"驳回\")"
    t.string "review_comment", comment: "审核意见"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "operating_at", comment: "操作时间"
    t.integer "organization_id", comment: "组织ID"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["approval_step_id"], name: "index_approval_step_users_on_approval_step_id"
    t.index ["organization_id"], name: "index_approval_step_users_on_organization_id"
    t.index ["user_id"], name: "index_approval_step_users_on_user_id"
  end

  create_table "approval_steps", force: :cascade do |t|
    t.integer "approval_flow_id", comment: "审核步骤ID"
    t.integer "order_number", comment: "步骤顺序"
    t.string "name", comment: "步骤名称"
    t.integer "review_type", comment: "审核类型: 1: 会签（需所有审批人同意)、2: 或签(一名审批人同意即可)、3: 依次审批（按顺序依次审批）"
    t.integer "status", comment: "状态(1: 等待中, 2: \"进行中\", 3: \"已完成\", 4: \"驳回\")"
    t.datetime "deleted_at", comment: "删除时间"
    t.integer "organization_id", comment: "组织ID"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["approval_flow_id"], name: "index_approval_steps_on_approval_flow_id"
    t.index ["organization_id"], name: "index_approval_steps_on_organization_id"
  end

  create_table "chip_configs", force: :cascade do |t|
    t.string "name", comment: "芯片配置名称"
    t.integer "product_line", comment: "产品线"
    t.integer "c_type", comment: "芯片类型"
    t.integer "product_category_id", comment: "产品类型ID"
    t.string "description", comment: "描述"
    t.string "code", comment: "芯片编码"
    t.boolean "status", default: true, comment: "状态"
    t.datetime "deleted_at"
    t.integer "organization_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_id"], name: "index_chip_configs_on_organization_id"
  end

  create_table "chip_os_softwares", force: :cascade do |t|
    t.string "name", comment: "软件名称"
    t.integer "chip_config_id", comment: "芯片平台ID"
    t.integer "organization_id"
    t.datetime "deleted_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["chip_config_id"], name: "index_chip_os_softwares_on_chip_config_id"
    t.index ["organization_id"], name: "index_chip_os_softwares_on_organization_id"
  end

  create_table "chip_os_versions", force: :cascade do |t|
    t.string "version", comment: "版本号"
    t.integer "chip_os_software_id", comment: "软件ID"
    t.boolean "is_default", default: false, comment: "是否默认"
    t.datetime "deleted_at"
    t.integer "organization_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["chip_os_software_id"], name: "index_chip_os_versions_on_chip_os_software_id"
    t.index ["organization_id"], name: "index_chip_os_versions_on_organization_id"
  end

  create_table "comments", force: :cascade do |t|
    t.integer "user_id", comment: "用户ID"
    t.string "commentable_type", comment: "评论对象类型"
    t.integer "commentable_id", comment: "评论对象ID"
    t.integer "parent_id", comment: "父级ID"
    t.text "content", comment: "评论内容"
    t.integer "organization_id", comment: "组织ID"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["commentable_type", "commentable_id"], name: "index_comments_on_commentable_type_and_commentable_id"
    t.index ["organization_id"], name: "index_comments_on_organization_id"
    t.index ["user_id"], name: "index_comments_on_user_id"
  end

  create_table "editor_files", force: :cascade do |t|
    t.string "file"
    t.integer "file_type"
    t.string "file_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "url"
  end

  create_table "flow_steps", force: :cascade do |t|
    t.integer "flow_version_id", comment: "流程版本ID"
    t.integer "order_number", comment: "步骤顺序"
    t.string "name", comment: "步骤名称"
    t.integer "review_type", comment: "审核类型: 1: 会签（需所有审批人同意)、2: 或签(一名审批人同意即可)、3: 依次审批（按顺序依次审批）"
    t.string "review_user_ids", comment: "审核人用户ID列表"
    t.datetime "deleted_at", comment: "删除时间"
    t.integer "organization_id", comment: "组织ID"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["flow_version_id"], name: "index_flow_steps_on_flow_version_id"
    t.index ["organization_id"], name: "index_flow_steps_on_organization_id"
  end

  create_table "flow_versions", force: :cascade do |t|
    t.integer "version", comment: "版本号"
    t.boolean "status", default: false, comment: "启用状态"
    t.integer "organization_flow_id", comment: "流程ID"
    t.datetime "deleted_at", comment: "删除时间"
    t.integer "organization_id", comment: "组织ID"
    t.integer "user_id", comment: "用户ID"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_flow_id"], name: "index_flow_versions_on_organization_flow_id"
    t.index ["organization_id"], name: "index_flow_versions_on_organization_id"
    t.index ["user_id"], name: "index_flow_versions_on_user_id"
  end

  create_table "group_users", force: :cascade do |t|
    t.integer "group_id", comment: "用户组ID"
    t.integer "user_id", comment: "用户ID"
    t.integer "organization_id", comment: "组织ID"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["group_id", "user_id"], name: "index_group_users_on_group_and_user", unique: true
    t.index ["group_id"], name: "index_group_users_on_group_id"
    t.index ["organization_id"], name: "index_group_users_on_organization_id"
    t.index ["user_id"], name: "index_group_users_on_user_id"
  end

  create_table "groups", force: :cascade do |t|
    t.string "name", comment: "用户组名称"
    t.text "description", comment: "用户组描述"
    t.integer "organization_id", comment: "组织ID"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_id"], name: "index_groups_on_organization_id"
  end

  create_table "organization_flows", force: :cascade do |t|
    t.string "name", comment: "流程名称"
    t.string "description", comment: "流程描述"
    t.integer "flow_type", comment: "使用类型"
    t.datetime "deleted_at", comment: "删除时间"
    t.integer "organization_id", comment: "组织ID"
    t.integer "user_id", comment: "用户ID"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_id"], name: "index_organization_flows_on_organization_id"
    t.index ["user_id"], name: "index_organization_flows_on_user_id"
  end

  create_table "organization_permission_controllers", force: :cascade do |t|
    t.integer "organization_id", comment: "企业ID"
    t.integer "permission_controller_id", comment: "权限控制器ID"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_id", "permission_controller_id"], name: "index_org_perm_ctrl_on_org_id_and_perm_ctrl_id", unique: true
    t.index ["organization_id"], name: "index_org_perm_ctrl_on_org_id"
    t.index ["permission_controller_id"], name: "index_org_perm_ctrl_on_perm_ctrl_id"
  end

  create_table "organizations", force: :cascade do |t|
    t.string "name", comment: "企业名称"
    t.integer "org_type", comment: "企业类型: 1: 代理商 2: 方案商 3: 品牌商"
    t.integer "parent_id", comment: "父级企业ID"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["parent_id"], name: "index_organizations_on_parent_id"
  end

  create_table "permission_actions", force: :cascade do |t|
    t.string "name", comment: "动作名称"
    t.string "word", comment: "action"
    t.integer "order_number", comment: "排序"
    t.integer "organization_id", comment: "组织ID"
    t.integer "permission_controller_id", comment: "控制器ID"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["deleted_at"], name: "index_permission_actions_on_deleted_at"
    t.index ["organization_id"], name: "index_permission_actions_on_organization_id"
    t.index ["permission_controller_id"], name: "index_permission_actions_on_permission_controller_id"
  end

  create_table "permission_controllers", force: :cascade do |t|
    t.string "name", comment: "控制器名称"
    t.string "word", comment: "controller"
    t.integer "order_number", comment: "排序"
    t.integer "organization_id", comment: "组织ID"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["deleted_at"], name: "index_permission_controllers_on_deleted_at"
    t.index ["organization_id"], name: "index_permission_controllers_on_organization_id"
  end

  create_table "plans", force: :cascade do |t|
    t.integer "order_number", comment: "序号"
    t.integer "level", comment: "等级"
    t.string "name", comment: "计划名称"
    t.string "content", comment: "计划内容"
    t.datetime "started_at", comment: "计划开始时间"
    t.datetime "ended_at", comment: "计划结束时间"
    t.datetime "act_started_at", comment: "实际开始时间"
    t.datetime "act_ended_at", comment: "实际结束时间"
    t.integer "status", comment: "状态"
    t.integer "p_type", comment: "类型"
    t.integer "p_priority", comment: "优先级"
    t.integer "parent_id", comment: "父计划ID"
    t.integer "project_id", comment: "项目ID"
    t.integer "user_id", comment: "用户ID"
    t.integer "duty_user_id", comment: "责任人ID"
    t.datetime "deleted_at", comment: "删除时间"
    t.integer "organization_id", comment: "组织ID"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["duty_user_id"], name: "index_plans_on_duty_user_id"
    t.index ["organization_id"], name: "index_plans_on_organization_id"
    t.index ["project_id"], name: "index_plans_on_project_id"
    t.index ["user_id"], name: "index_plans_on_user_id"
  end

  create_table "product_categories", force: :cascade do |t|
    t.string "name"
    t.integer "organization_id"
    t.datetime "deleted_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_id"], name: "index_product_categories_on_organization_id"
  end

  create_table "project_organizations", force: :cascade do |t|
    t.integer "project_id", comment: "项目ID"
    t.integer "organization_id", comment: "组织ID"
    t.integer "p_type", comment: "组织类型 1: 组织者 2: 成员"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "status"
    t.datetime "operated_at"
    t.index ["organization_id"], name: "index_project_organizations_on_organization_id"
    t.index ["project_id"], name: "index_project_organizations_on_project_id"
  end

  create_table "project_permission_configs", force: :cascade do |t|
    t.integer "order_number", comment: "排序"
    t.string "key", limit: 200, comment: "权限key"
    t.string "name", comment: "权限名称"
    t.integer "parent_id", comment: "父级id"
    t.integer "organization_id", comment: "外键: 组织id"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_id"], name: "index_project_permission_configs_on_organization_id"
  end

  create_table "project_risks", force: :cascade do |t|
    t.integer "project_id", comment: "项目ID"
    t.string "riskable_type"
    t.bigint "riskable_id", comment: "多态"
    t.string "name", comment: "风险名称"
    t.integer "risk_type", comment: "风险类型"
    t.string "which_module", comment: "所属模块"
    t.integer "aasm_state", comment: "状态"
    t.string "description", comment: "风险描述"
    t.integer "risk_level", comment: "风险等级"
    t.integer "nature", comment: "风险性质"
    t.integer "problem_severity", comment: "影响程度"
    t.integer "coping_strategy", comment: "应对策略"
    t.text "action_plan", comment: "行动计划"
    t.string "close_reason", comment: "关闭原因"
    t.datetime "started_at", comment: "开始时间"
    t.datetime "ended_at", comment: "结束时间"
    t.datetime "act_started_at", comment: "实际起始时间"
    t.datetime "act_ended_at", comment: "时间结束时间"
    t.datetime "deleted_at", comment: "删除时间"
    t.integer "obligation_user_id", comment: "责任人ID"
    t.integer "user_id", comment: "创建人ID"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["obligation_user_id"], name: "index_project_risks_on_obligation_user_id"
    t.index ["project_id"], name: "index_project_risks_on_project_id"
    t.index ["riskable_type", "riskable_id"], name: "index_project_risks_on_riskable"
    t.index ["user_id"], name: "index_project_risks_on_user_id"
  end

  create_table "project_role_configs", force: :cascade do |t|
    t.string "name", comment: "角色名称"
    t.boolean "is_default", default: false, comment: "是否默认角色"
    t.integer "organization_id", comment: "外键: 组织id"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_id"], name: "index_project_role_configs_on_organization_id"
  end

  create_table "project_role_permissions", force: :cascade do |t|
    t.integer "project_permission_config_id", comment: "外键: 项目权限配置ID"
    t.integer "project_role_config_id", comment: "外键: 角色配置ID"
    t.integer "organization_id", comment: "外键: 组织id"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_id"], name: "index_project_role_permissions_on_organization_id"
    t.index ["project_permission_config_id"], name: "index_project_role_permissions_on_project_permission_config_id"
    t.index ["project_role_config_id"], name: "index_project_role_permissions_on_project_role_config_id"
  end

  create_table "project_users", force: :cascade do |t|
    t.integer "user_id", comment: "外键: 用户id"
    t.integer "project_id", comment: "外键: 项目id"
    t.integer "project_role_config_id", comment: "外键: 角色配置ID"
    t.integer "organization_id", comment: "外键: 组织id"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_id"], name: "index_project_users_on_organization_id"
    t.index ["project_id"], name: "index_project_users_on_project_id"
    t.index ["project_role_config_id"], name: "index_project_users_on_project_role_config_id"
    t.index ["user_id"], name: "index_project_users_on_user_id"
  end

  create_table "projects", force: :cascade do |t|
    t.string "name", comment: "项目名称"
    t.integer "chip_config_id", comment: "芯片平台ID"
    t.integer "project_type", comment: "项目类型"
    t.string "product_name", comment: "产品名称"
    t.integer "product_category_id", comment: "产品类型ID"
    t.text "description", comment: "项目描述"
    t.string "main_purpose", comment: "项目目标、主要目的"
    t.datetime "design_in_at", comment: "立项时间"
    t.datetime "evt_at", comment: "工程测试时间"
    t.datetime "dvt_at", comment: "设计验证时间"
    t.datetime "pvt_at", comment: "小批量过程验证时间"
    t.datetime "mp_at", comment: "批量生产时间"
    t.integer "status", comment: "状态"
    t.decimal "fcst_per_month", precision: 14, scale: 2, comment: "FCST/月"
    t.decimal "mp_plus_six_months", precision: 14, scale: 2, comment: "MP+6个月"
    t.string "specification_file", comment: "规格附件"
    t.integer "user_id", comment: "用户ID、项目创建人"
    t.datetime "deleted_at", comment: "删除时间"
    t.string "terminal_customer_name", comment: "终端客户简称"
    t.integer "chip_os_version_id", comment: "OS系统软件版本ID"
    t.integer "chip_os_software_id", comment: "OS系统软件ID"
    t.integer "team_size", comment: "项目人数"
    t.string "customer_project_name", comment: "客户项目简称"
    t.string "target_market_region", comment: "目标市场区域"
    t.boolean "agreement_accepted", comment: "是否同意协议"
    t.string "username", comment: "姓名"
    t.string "phone", comment: "联系电话"
    t.string "email", comment: "邮箱"
    t.string "acceptor_name", comment: "业务受理人"
    t.datetime "opening_at", comment: "开案时间"
    t.text "opening_desc", comment: "开案说明"
    t.string "main_competitiveness", comment: "产品主要功能"
    t.integer "product_manager_id", comment: "产品经理"
    t.integer "technical_manager_id", comment: "技术经理"
    t.integer "business_contact_id", comment: "业务负责人"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "chip_organization_id", comment: "芯片归属组织"
    t.index ["chip_config_id"], name: "index_projects_on_chip_config_id"
    t.index ["chip_organization_id"], name: "index_projects_on_chip_organization_id"
    t.index ["chip_os_software_id"], name: "index_projects_on_chip_os_software_id"
    t.index ["chip_os_version_id"], name: "index_projects_on_chip_os_version_id"
    t.index ["product_category_id"], name: "index_projects_on_product_category_id"
    t.index ["user_id"], name: "index_projects_on_user_id"
  end

  create_table "public_logs", force: :cascade do |t|
    t.string "content"
    t.string "key"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "role_permissions", force: :cascade do |t|
    t.integer "role_id", comment: "角色ID"
    t.integer "permission_action_id", comment: "权限ID"
    t.integer "organization_id", comment: "组织ID"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["deleted_at"], name: "index_role_permissions_on_deleted_at"
    t.index ["organization_id"], name: "index_role_permissions_on_organization_id"
    t.index ["permission_action_id"], name: "index_role_permissions_on_permission_action_id"
    t.index ["role_id"], name: "index_role_permissions_on_role_id"
  end

  create_table "roles", force: :cascade do |t|
    t.string "name", comment: "角色名称"
    t.text "description", comment: "角色描述"
    t.integer "organization_id", comment: "组织ID"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["deleted_at"], name: "index_roles_on_deleted_at"
    t.index ["organization_id"], name: "index_roles_on_organization_id"
  end

  create_table "system_configs", force: :cascade do |t|
    t.integer "organization_id", comment: "组织ID"
    t.string "feishu_app_id", comment: "飞书应用ID"
    t.string "feishu_app_secret", comment: "飞书应用密钥"
    t.string "ding_agent_id", comment: "钉钉应用ID"
    t.string "ding_app_key", comment: "钉钉应用Key"
    t.string "ding_app_secret", comment: "钉钉应用密钥"
    t.string "qiye_app_id", comment: "企微应用ID"
    t.string "qiye_agent_id", comment: "企微应用AgentID"
    t.string "qiye_secret", comment: "企微应用Secret"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_id"], name: "index_system_configs_on_organization_id"
  end

  create_table "user_notices", force: :cascade do |t|
    t.integer "user_id", comment: "用户id"
    t.string "name", comment: "标题"
    t.text "content", comment: "内容"
    t.integer "organization_id", comment: "组织id"
    t.string "noticeable_type"
    t.bigint "noticeable_id", comment: "多态"
    t.datetime "deleted_at", comment: "删除时间"
    t.boolean "status", default: false, comment: "阅读状态"
    t.string "url", comment: "跳转链接"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["noticeable_type", "noticeable_id"], name: "index_user_notices_on_noticeable"
    t.index ["organization_id"], name: "index_user_notices_on_organization_id"
    t.index ["user_id"], name: "index_user_notices_on_user_id"
  end

  create_table "user_roles", force: :cascade do |t|
    t.integer "user_id", comment: "用户ID"
    t.integer "role_id", comment: "角色ID"
    t.integer "organization_id", comment: "组织ID"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["deleted_at"], name: "index_user_roles_on_deleted_at"
    t.index ["organization_id"], name: "index_user_roles_on_organization_id"
    t.index ["role_id"], name: "index_user_roles_on_role_id"
    t.index ["user_id"], name: "index_user_roles_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "name", limit: 200
    t.string "phone", limit: 200
    t.string "password_digest"
    t.string "avatar", limit: 200
    t.integer "recommend_user_id"
    t.integer "status"
    t.datetime "last_login_at"
    t.datetime "deleted_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "email", comment: "邮箱"
    t.datetime "actived_at", comment: "激活时间"
    t.datetime "locked_at", comment: "锁定时间"
    t.boolean "is_admin", default: false, comment: "是否管理员"
    t.integer "organization_id", comment: "组织ID"
    t.string "feishu_user_id"
    t.index ["feishu_user_id"], name: "index_users_on_feishu_user_id", unique: true
    t.index ["organization_id"], name: "index_users_on_organization_id"
  end

  create_table "verify_codes", force: :cascade do |t|
    t.string "code", limit: 50
    t.datetime "deleted_at"
    t.datetime "expired_at"
    t.string "phone", limit: 50
    t.datetime "used_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "versions", force: :cascade do |t|
    t.bigint "whodunnit", comment: "用户ID"
    t.datetime "created_at", comment: "创建时间"
    t.datetime "updated_at", comment: "更新时间"
    t.bigint "item_id", null: false, comment: "多态记录ID"
    t.string "item_type", null: false, comment: "多态记录类型"
    t.string "event", null: false, comment: "变动事件"
    t.json "object", comment: "变动记录对象"
    t.json "object_changes", comment: "变动记录：字段由什么变动为什么"
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  create_table "work_order_reason_logs", force: :cascade do |t|
    t.integer "work_order_id", comment: "工单Id"
    t.string "reason", comment: "理由"
    t.integer "reason_type", comment: "理由类型"
    t.datetime "deleted_at", comment: "删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["work_order_id"], name: "index_work_order_reason_logs_on_work_order_id"
  end

  create_table "work_orders", force: :cascade do |t|
    t.string "aasm_state", comment: "状态 ｜ 问题状态"
    t.string "workable_type"
    t.bigint "workable_id", comment: "多态"
    t.integer "work_type", comment: "工单类型 ｜ 问题类型"
    t.string "title", comment: "标题"
    t.string "description", comment: "问题描述"
    t.string "customer_name", comment: "客户名称"
    t.integer "priority", comment: "优先级"
    t.string "product_name", comment: "产品名称"
    t.string "hardware_version", comment: "硬件版本号"
    t.string "file", comment: "文件"
    t.integer "receiver_user_id", comment: "接收人"
    t.string "which_module", comment: "所属模块"
    t.integer "problem_severity", comment: "问题严重性"
    t.boolean "is_platform_commonality", comment: "是否是平台共性"
    t.integer "chip_config_id", comment: "芯片平台ID"
    t.integer "chip_os_software_id", comment: "OS系统软件ID, 软件"
    t.integer "chip_os_version_id", comment: "OS系统软件版本ID, 软件版本"
    t.integer "product_category_id", comment: "产品类型ID"
    t.integer "demand_sources", comment: "需求来源"
    t.text "repro_steps", comment: "重现步骤"
    t.datetime "started_at", comment: "开始时间"
    t.datetime "ended_at", comment: "结束时间"
    t.integer "founder_id", comment: "创建人"
    t.string "founder_email", comment: "创建邮箱"
    t.string "founder_phone", comment: "创建电话"
    t.integer "project_progress", comment: "项目进度"
    t.datetime "deleted_at", comment: "软删除、删除时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "project_id", comment: "项目ID"
    t.integer "obligation_user_id", comment: "责任人ID"
    t.string "rejection_reason", comment: "驳回理由"
    t.string "solution", comment: "解决方案"
    t.string "customer_confirmation", comment: "客户确认"
    t.datetime "act_started_at", comment: "实际开始时间"
    t.datetime "act_ended_at", comment: "实际结束时间"
    t.datetime "closed_at", comment: "关闭时间"
    t.index ["chip_config_id"], name: "index_work_orders_on_chip_config_id"
    t.index ["chip_os_version_id"], name: "index_work_orders_on_chip_os_version_id"
    t.index ["obligation_user_id"], name: "index_work_orders_on_obligation_user_id"
    t.index ["product_category_id"], name: "index_work_orders_on_product_category_id"
    t.index ["project_id"], name: "index_work_orders_on_project_id"
    t.index ["receiver_user_id"], name: "index_work_orders_on_receiver_user_id"
    t.index ["workable_type", "workable_id"], name: "index_work_orders_on_workable"
  end

end
